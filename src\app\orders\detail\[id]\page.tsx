"use client";
import React, { useEffect, useState, useCallback, Fragment } from "react";
import Cookies from "js-cookie";
import Sidebar from "../../../components/sidebar";
import OrderActionModal from "@/app/components/orderActionModal";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import axios from "axios";
import config from "../../../../../config.json";
import currency from "../../../../../Currency.json";
import { Dialog, Transition } from "@headlessui/react";
import Link from "next/link";
import { ArrowPathIcon } from "@heroicons/react/20/solid";
import { formatToAMPM } from "@/app/utils/auth";
import Image from "next/image";
import summaryIcon from "../../../assets/summaryIcon.png";
import distributorIcon from "../../../assets/distributorIcon.png";
import { IoMdArrowBack } from "react-icons/io";
import UploadSheetModal from "@/app/components/UploadSheetModal";
import SuccessNotification from "@/app/components/SuccessNotifications";
import ErrorNotification from "@/app/components/ErrorNotification";
import { toast, ToastContainer } from "react-toastify";

const queryClient = new QueryClient();
export default function Orders({ params }) {
  const [detailsTableData, setdetailsTableData] = useState([]);
  const [shipmentTableData, setShipmentTableData] = useState([]);
  const [attributeTableData, setattributeTableData] = useState([]);
  const [orderNumber, setOrderNumber] = useState([]);
  const [omsOrderNumber, setOmsOrderNumber] = useState("");
  const [statusOptions, setStatusOptions] = useState([]);
  const [timeLineData, setTimeLineData] = useState([]);
  const [open, setOpen] = useState(false);
  const [openOrderAction, setOpenOrderAction] = useState(false);
  const [orderActionType, setOrderActionType] = useState("");
  const [orderIDShipmentStatus, setOrderIDShipmentStatus] = useState("");
  const [shipmentIDShipmentStatus, setShipmentIDShipmentStatus] = useState("");
  const [orderStatus, setOrderStatus] = useState("");
  const [pseudoId, setPseudoId] = useState("");
  const [message, setMessage] = useState("");
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [sageData, setSageData] = useState({
    sageOrderNumber: "",
    sageOrderStatus: "",
    sageUniqueId: "",
  });
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [bulkProcessLoading, setBulkProcessLoading]: any = useState(false);
  const [bulkProcessError, setBulkProcessError]: any = useState(null);
  const [userRole, setUserRole]: any = useState("");

  let [summaryDataDetails, setSummaryDataDetails]: any = useState({
    orderTotal: "",
    createdAt: "",
    totalItems: "",
    country: "",
    dID: "",
    dName: "",
    dEmail: "",
    dPhone: "",
    currency: "",
  });
  const [tabs, setTabs] = useState([
    { name: "Details", current: true },
    // { name: "Shipment", current: false },
    // { name: "Attributes", current: false },
    { name: "Timeline", current: false },
  ]);

  function changeTab(name: any) {
    setTabs((prevTabs) =>
      prevTabs.map((tab) =>
        tab.name === name
          ? { ...tab, current: true }
          : { ...tab, current: false }
      )
    );
  }

  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  async function continueChangeStatus() {
    setOpen(false);
    changeStatus(orderIDShipmentStatus, shipmentIDShipmentStatus);
  }
  async function changeStatus(orderId: any, shipmentId: any) {
    setOrderIDShipmentStatus(orderId);
    setShipmentIDShipmentStatus(shipmentId);
    let id = (document.getElementById("location") as HTMLFormElement).value
      .toString()
      .split("##")[0];
    let value = (
      document.getElementById("location") as HTMLFormElement
    ).value.split("##")[1];
    let flag = 0;
    let message = `Status changed to ${value}`;
    let utr = "";
    if (value === "Finance approved") {
      setOpen(true);
      flag++;
      utr = (document.getElementById("utrNumber") as HTMLFormElement).value;
      message = `Status changed to ${value} with UTR - ${utr}`;
    }
    await axios.request({
      method: "patch",
      url: `${config.BACKEND_URL}/shipment/${shipmentId}`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        status: id,
      }),
    });
    await axios.request({
      method: "post",
      url: `${config.BACKEND_URL}/timeline`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        category: "Shipment",
        message: message,
        user: "Default user",
        order: orderId,
        shipment: shipmentId,
        actionType: "Status changed",
        journey: "",
      }),
    });
    setOpen(false);
    await fetchOrderDataSingle();
    await fetchOrderShipments();
    // await fetchOrderTimeline();
  }
  async function fetchOrderShipments() {
    let statusID = "";
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/order/${params.id}/shipments`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("token")}`,
      },
    });

    let shipmentData: any = [];
    response?.data?.data?.shipments?.length > 0 &&
      response.data.data.shipments.forEach((x: any) => {
        statusID = x?._id;
        shipmentData.push({
          name: x.name,
          id: x._id,
          internalRef: x.internalRef,
          status: x.status?.status,
          colorCode: x.status?.colorCode,
          items: x.items,
          createdAt: new Date(x.createdAt).toString().slice(0, 15),
          amount: x.amount,
          currency: currency[x.shipping_address?.country],
          shipmentID: x._id,
          orderID: x.order._id,
          from: "Sunrise WH",
        });
      });

    setShipmentTableData(shipmentData);
    // response.data.data.data.length > 0 && fetchShipmentFlowData(statusID);
  }

  const handleFileUpload = async (file: File) => {
    setBulkProcessLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      const response = await fetch(
        `${config.BACKEND_URL}/action/autoAllocate`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          body: formData,
        }
      );

      // Check if response is a file (Excel/CSV) by checking content-type
      const contentType = response.headers.get("content-type");
      const contentDisposition = response.headers.get("content-disposition");

      if (
        (contentType &&
          (contentType.includes(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          ) ||
            contentType.includes("application/vnd.ms-excel") ||
            contentType.includes("text/csv") ||
            contentType.includes("application/octet-stream"))) ||
        (contentDisposition && contentDisposition.includes("attachment"))
      ) {
        setErrorMessage(
          "File processing failed. Downloading an error sheet with details in 3 seconds..."
        );
        setShowErrorNotification(true);

        // Response is a file, prepare for download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // Extract filename from content-disposition header or use default
        let filename = "error_report.xlsx";
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(
            /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
          );
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, "");
          }
        }

        link.download = filename;

        setBulkProcessLoading(false);
        setConfirmationModalOpen(false);
        setBulkProcessError(null);

        // Wait 3 seconds before starting download
        setTimeout(() => {
          setErrorMessage("Downloading error sheet now...");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          // Hide notification after download starts
          setTimeout(() => {
            setShowErrorNotification(false);
          }, 2000);
        }, 3000);

        return;
      }

      // Response is JSON, handle normally
      const data = await response.json();
      if (data.status === "success") {
        setConfirmationModalOpen(false);
        setBulkProcessLoading(false);
        setBulkProcessError(null);
        setSuccessMessage("File uploaded successfully!");
        setShowSuccessNotification(true);

        // Hide success notification after 5 seconds
        setTimeout(() => {
          setShowSuccessNotification(false);
        }, 5000);
      } else {
        setBulkProcessLoading(false);
        setBulkProcessError(data.message);
        setErrorMessage(data.message || "File upload failed");
        setShowErrorNotification(true);

        // Hide error notification after 5 seconds
        setTimeout(() => {
          setShowErrorNotification(false);
        }, 5000);
      }
    } catch (error) {
      setBulkProcessLoading(false);
      setBulkProcessError("An error occurred during file upload.");
      setErrorMessage("An error occurred during file upload.");
      setShowErrorNotification(true);

      setTimeout(() => {
        setShowErrorNotification(false);
      }, 5000);
    }
  };

  async function fetchOrderDataSingle() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/order/${params.id}`,
      headers: {
        "Content-Type": "application/json",
      },
    });

    setOrderNumber(response.data.data.data.name);
    setOmsOrderNumber(response.data.data.data.subOrderId);
    let dID = response?.data?.data?.data?.distributor?.customerNumber;
    let dName = response?.data?.data?.data?.distributor?.name;
    let dEmail = response?.data?.data?.data?.distributor?.email;
    let totalQty = 0;
    let country = response?.data?.data?.data?.shipping_address?.country;
    if (!country) country = "MY";
    let currencyy = currency[country];
    let attributeData = response.data.data.data.attributes.map((x: any) => {
      const emailsByStatus = Array.isArray(x.value)
        ? x.value.reduce((acc, item) => {
            if (item.email) {
              const status = item.status;
              const emails =
                item.email || (item.emailInfo || []).map((info) => info.email);
              if (emails) {
                acc[status] = (acc[status] || []).concat(emails); // Initialize or concatenate emails
              }
              return acc;
            } else {
              return x.value;
            }
          }, {})
        : x.value;
      return {
        name: x.name,
        type: x.type,
        value: JSON.stringify(emailsByStatus),
      };
    });
    let data = response.data.data.data.line_items.map((x: any) => {
      totalQty += x.quantity;
      return {
        sku: x.sku,
        id: x._id,
        title: x.productTitle,
        itemPrice: x.price,
        qty: x.quantity,
        requested: x.requested || x.quantity,
        fulfilled: x.fulfilled,
        remaining: x.remaining,
        totalPrice: parseFloat(x.price) * parseFloat(x.quantity),
        currency: currencyy,
        metafieldValue: x.metaFieldBudgetCategory,
        preOrderValue: response?.data?.data?.data?.preOrderValue,
        isFuture: response.data.data.data.isFuture,
      };
    });

    let orderTotalPrice = response.data.data.data.line_items.reduce(
      (value, total) => {
        let totalPrice = total.price * total.quantity;
        value += totalPrice;
        return value;
      },
      0
    );

    let timelineData = response.data.data.data.timeline.map((x: any) => {
      return {
        date:
          new Date(x.time).toString().slice(0, 15) + " " + formatToAMPM(x.time),
        comments: x.comment,
      };
    });
    setTimeLineData(timelineData);

    setSummaryDataDetails({
      orderTotal: orderTotalPrice.toFixed(2),
      createdAt: new Date(response.data.data.data.createdAt)
        .toString()
        .slice(0, 15),
      totalItems: totalQty,
      country: country,
      dID: dID,
      dName: dName,
      dEmail: dEmail,
      currency: currencyy,
    });
    setdetailsTableData(data);
    setattributeTableData(attributeData);
    setOrderStatus(response.data.data.data.status);
    setPseudoId(response.data.data.data.status.pseudoId);
    setSageData({
      sageOrderNumber: response.data.data.data.sageOrderNumber || "-",
      sageOrderStatus: response.data.data.data.sageStatus || "-",
      sageUniqueId: response.data.data.data.erpIdentifier || "-",
    });

    setTimeout(() => {
      setRefreshButtonClicked(false);
    }, 1000);
  }

  async function fetchShipmentFlowData(statusID: any) {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/status_flow?statusId=${statusID}`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        status: x.toStatusId.status,
        id: x.toStatusId._id,
      };
    });
    setStatusOptions(data);
  }

  useEffect(() => {
    fetchOrderDataSingle();
    fetchOrderShipments();
    // fetchOrderTimeline();
  }, [tabs]);
  useEffect(() => {
    let timeout;
    if (message !== "") {
      timeout = setTimeout(() => {
        setMessage("");
      }, 7000);
    }
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [message]);

  function autoAllocate() {
    setOpenOrderAction(true);
    setOrderActionType("autoAllocate");
  }

  function allocateManually() {
    setOpenOrderAction(true);
    setOrderActionType("allocateManually");
  }

  function rejectOrder() {
    setOpenOrderAction(true);
    setOrderActionType("rejectOrder");
  }

  useEffect(() => {
    if (showSuccessNotification) {
      toast.success(`${successMessage}`);
      setRefreshButtonClicked(true);
      fetchOrderDataSingle();
      fetchOrderShipments();
    }
  }, [showSuccessNotification, successMessage]);

  // Toast notification effect for error
  useEffect(() => {
    if (showErrorNotification) {
      toast.error(`${errorMessage}`);
      setRefreshButtonClicked(true);
      fetchOrderDataSingle();
      fetchOrderShipments();
    }
  }, [showErrorNotification, errorMessage]);

  useEffect(() => {
    // fetchOrderData(1);
    // fetchOrderStatus();

    const savedFilters = Cookies.get("orderFilter");
    if (savedFilters) {
      // setAlreadyAppliedFilters(JSON.parse(savedFilters));
      Cookies.remove("orderFilter");
    }
    setUserRole(Cookies.get("userrole"));
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <OrderActionModal
        open={openOrderAction}
        setOpen={setOpenOrderAction}
        type={orderActionType}
        orderId={params.id}
        fetchOrderDataSingle={fetchOrderDataSingle} // Pass refresh function
        message={message}
        setMessage={setMessage}
      />

      <UploadSheetModal
        isOpen={confirmationModalOpen}
        onClose={() => setConfirmationModalOpen(false)}
        onSubmit={handleFileUpload}
        sampleFileUrl={`${config.BACKEND_URL}/order/pending/orders?orderIds=${params.id}`}
        title="Upload Sheet"
        acceptedFileTypes=".xlsx,.xls,.csv"
        fullStockSheetUrl=""
        loading={bulkProcessLoading}
        firstLinkName="Download Orders Allocation Sheet"
        warningNote="Action Meaning (for reference)\nFUTURE: A new order with the remaining quantity will be created in SAGE with type marked as FUTURE.\nALLOCATE: Remaining quantity will be created in a new order under Pending Alignment status in the OMS.\nREJECT: A separate order will be created with the pending quantity, but its status will be set to Cancelled."
      />
      {/* 
      {showSuccessNotification && (
        <SuccessNotification message={successMessage} />
      )}
      {showErrorNotification && <ErrorNotification message={errorMessage} />} */}

      <Sidebar>
        <div className="flex p-4 items-center justify-between">
          <div className="flex gap-3 items-center">
            <Link href="/orders">
              <IoMdArrowBack size={30} fill="black" />
            </Link>
            <h2 className="text-3xl font-semibold leading-6 text-gray-900">
              Order: {omsOrderNumber}
            </h2>
          </div>

          <div className="flex gap-3 items-center">
            <button
              type="button"
              className="flex justify-center items-center rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              onClick={() => {
                setRefreshButtonClicked(true);
                fetchOrderDataSingle();
                fetchOrderShipments();
              }}
            >
              <ArrowPathIcon
                className={`${
                  refreshButtonClicked ? "animate-spin" : "animate-none"
                } h-5 w-5`}
              />
            </button>

            {(userRole === "Sunrise Admin" ||
              userRole === "Finance Manager" ||
              userRole === "Finance Executive" ||
              userRole === "Sales Person") && (
              <button
                onClick={() => {
                  setConfirmationModalOpen(true);
                }}
                type="button"
                disabled={
                  bulkProcessLoading ||
                  (orderStatus.toLowerCase() !== "pending" &&
                    orderStatus.toLowerCase() !== "created")
                }
                className={`rounded-md whitespace-nowrap px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
                  bulkProcessLoading
                    ? "bg-gray-400 cursor-not-allowed"
                    : "bg-[#222222]"
                }`}
              >
                Bulk Process
              </button>
            )}
          </div>
        </div>
        <Transition.Root show={open} as={Fragment}>
          <Dialog className="relative z-10" onClose={setOpen}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>

            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                    <div>
                      <label
                        htmlFor="utrNumber"
                        className="block text-sm font-medium leading-6 text-gray-900"
                      >
                        UTR No.
                      </label>
                      <div className="mt-2">
                        <input
                          type="text"
                          name="utrNumber"
                          id="utrNumber"
                          className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                    </div>
                    <div className="flex justify-center py-2">
                      <button
                        type="button"
                        onClick={() => {
                          continueChangeStatus();
                        }}
                        className="rounded bg-indigo-600 px-2 py-1 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                      >
                        Submit
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>

        <div className="px-4 sm:px-6 lg:px-8">
          <div className="mt-8 flow-root">
            {message && (
              <p className="text-red-600 font-bold text-right">{message}</p>
            )}

            {/* Sage Order Information Table */}
            <div className="-mx-4 my-12 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-gray-900">
                    ECPAC Order Information
                  </h3>
                </div>
                <table className="rounded-lg shadow-sm min-w-full text-center divide-y divide-gray-300">
                  <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg text-white">
                    <tr className="divide-x divide-gray">
                      <th
                        scope="col"
                        className="rounded-tl-lg px-4 py-4 text-left text-lg font-bold"
                      >
                        ECPAC Order No.
                      </th>
                      <th
                        scope="col"
                        className="px-4 py-4 text-left text-lg font-bold"
                      >
                        ECPAC Order Unique Id
                      </th>
                      {/* <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold"
                      >
                        OMS Status
                      </th> */}
                      <th
                        scope="col"
                        className="rounded-tr-lg px-4 py-4 text-left text-lg font-bold"
                      >
                        Order Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    <tr className="even:bg-gray-50">
                      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-lg font-semibold text-gray-900 sm:pl-4">
                        {sageData.sageOrderNumber}
                      </td>
                      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-lg font-semibold text-gray-900 sm:pl-4">
                        {sageData.sageUniqueId}
                      </td>
                      <td className="whitespace-nowrap py-5 pl-4 pr-3 text-lg font-semibold text-gray-900 sm:pl-4">
                        <span
                          className={`inline-block px-4 py-2.5 rounded-lg text-base font-bold ${
                            orderStatus === "created"
                              ? "bg-gray-400 text-[#222222]"
                              : orderStatus === "pending"
                              ? "bg-blue-100 text-blue-800"
                              : orderStatus === "rejected"
                              ? "bg-red-100 text-red-800"
                              : orderStatus === "order placed "
                              ? "bg-sky-100 text-[#222222]"
                              : orderStatus === "invoiced"
                              ? "bg-purple-100 text-purple-800"
                              : orderStatus === "on hold"
                              ? " bg-orange-100 , text-[#222222]"
                              : orderStatus === "out for delivery"
                              ? "bg-[#D6FBF2] , text-[#222222]"
                              : orderStatus == "order completed"
                              ? "bg-green-100 text-green-800"
                              : orderStatus === "order deleted"
                              ? "bg-[#F4A4AB] , text-white"
                              : "bg-purple-100 text-purple-800"
                          }`}
                        >
                          {orderStatus?.toUpperCase()}
                        </span>
                      </td>
                      {/* <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                        <span className="inline-block px-2 py-1 rounded text-xs font-semibold bg-purple-100 text-purple-800">
                          {sageData.sageOrderStatus}
                        </span>
                      </td> */}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div>
              <div className="sm:hidden">
                <label htmlFor="tabs" className="sr-only">
                  Select a tab
                </label>
                <select
                  id="tabs"
                  name="tabs"
                  className="block w-full rounded-md border-gray-300 focus:border-[#E3D7D1] focus:ring-[#E3D7D1]"
                >
                  {tabs.map((tab) => (
                    <option key={tab.name}>{tab.name}</option>
                  ))}
                </select>
              </div>
              <div className="hidden sm:block">
                <nav
                  className="isolate flex divide-x divide-gray-200 rounded-lg shadow"
                  aria-label="Tabs"
                >
                  {tabs.map((tab, tabIdx) => (
                    <a
                      onClick={() => {
                        changeTab(tab.name);
                      }}
                      key={tab.name}
                      className={
                        tab.current
                          ? "cursor-pointer text-gray-900 group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-center text-sm font-medium hover:bg-gray-50 focus:z-10"
                          : "cursor-pointer text-[#222222] hover:text-gray-700 group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-center text-sm font-medium hover:bg-gray-50 focus:z-10"
                      }
                    >
                      <span>{tab.name}</span>
                      <span
                        aria-hidden="true"
                        className={
                          tab.current
                            ? "bg-[#373435] absolute inset-x-0 bottom-0 h-0.5"
                            : "bg-transparent absolute inset-x-0 bottom-0 h-0.5"
                        }
                      />
                    </a>
                  ))}
                </nav>
              </div>
            </div>
            <br></br>
            {tabs[0].current ? (
              <div>
                <br></br>
                <div className="flex space text-center">
                  {/* <div className="flex-none w-28"></div> */}
                  <div className="w-full	shadow-lg rounded-2xl flex-1 bg-white px-4 py-8 sm:px-6 border-solid border-1">
                    <div className="flex space-x-3">
                      <div className="w-[10%]">
                        <Image
                          src={summaryIcon}
                          width={50}
                          height={50}
                          alt="Summary Icon"
                        />
                      </div>
                      <div className="w-[90%] min-w-0 flex-1">
                        <h1 className="text-2xl text-left font-bold text-gray-900">
                          Summary
                        </h1>
                        <br></br>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-base">
                              Order Total :
                            </span>
                            <span className="pl-8 w-[60%] block text-base">
                              <span>{summaryDataDetails.currency}</span>{" "}
                              <span>{summaryDataDetails.orderTotal}</span>
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-base">
                              Creation Date :
                            </span>
                            <span className="pl-8 w-[60%] block text-base">
                              {summaryDataDetails.createdAt}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-base">
                              Total Items :
                            </span>
                            <span className="pl-8 w-[60%] block text-base">
                              {summaryDataDetails.totalItems}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block text-base w-[40%]">
                              Country :
                            </span>
                            <span className="pl-8 w-[60%] text-base block">
                              {summaryDataDetails.country}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex-none w-5"></div>
                  <div className="flex-1 w-full bg-white shadow-lg px-4 py-8 sm:px-6 border-solid border-1 rounded-2xl">
                    <div className=" flex space-x-3">
                      <div className="w-[10%]">
                        <Image
                          src={distributorIcon}
                          width={50}
                          height={50}
                          alt="Distributor Icon"
                        />
                      </div>
                      <div className="w-[90%] min-w-0 flex-1">
                        <h1 className="text-2xl text-left font-bold text-gray-900">
                          Customer Information
                        </h1>
                        <br></br>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="text-base block w-[40%]">
                              ECPAC Customer No. :{" "}
                            </span>

                            <span className="text-base pl-8 w-[80%] block">
                              {summaryDataDetails.dID}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block text-base w-[40%]">
                              Name :{" "}
                            </span>
                            <span className="pl-8 w-[80%] text-base block">
                              {summaryDataDetails.dName}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-base">
                              Email :{" "}
                            </span>
                            <span className="pl-8 w-[80%] block text-base break-words">
                              {summaryDataDetails.dEmail}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* <div className="flex-none w-28"></div> */}
                </div>
              </div>
            ) : (
              <div></div>
            )}
            <br></br>
            {tabs[0].current && (
              <div className="-mx-4 my-12 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full text-center divide-y divide-gray-300">
                    <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg text-white">
                      <tr className="divide-x divide-gray">
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-sm font-semibold sm:pl-3"
                        >
                          Index
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          SKU
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Title
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Item price
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Budget Category
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Pre Order Value
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Requested
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Fulfilled
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Remaining
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold"
                        >
                          Future
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-sm font-semibold"
                        >
                          Total price
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {detailsTableData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {i + 1}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.sku}
                          </td>
                          <td className="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.title}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.currency} {x.itemPrice}
                          </td>
                          <td className="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.metafieldValue}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.preOrderValue}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.requested}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.fulfilled}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.remaining}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.isFuture}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.currency} {x.totalPrice.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* {tabs[2].current &&  (
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#222222] text-center rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3  text-sm font-semibold text-white sm:pl-3"
                        >
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5  text-sm font-semibold text-white"
                        >
                          Type
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5  text-sm font-semibold text-white"
                        >
                          Value
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white text-center">
                      {attributeTableData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.name}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.type}
                          </td>
                          <td className="whitespace-pre-wrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.type == "JSON" ? (
                              <pre className="whitespace-pre-wrap">
                                {JSON.stringify(JSON.parse(x.value), null, 2)}
                              </pre>
                            ) : (
                              x.value
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )} */}
            {tabs[1].current && (
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Comments
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {timeLineData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.date}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.comments}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </Sidebar>
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </QueryClientProvider>
  );
}
