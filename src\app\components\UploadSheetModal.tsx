import React, { useState } from "react";
import Cookies from "js-cookie";
import { toast } from "react-toastify";

interface UploadSheetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (file: File) => void;
  sampleFileUrl: string;
  title: string;
  fullStockSheetUrl: string;
  acceptedFileTypes?: string;
  loading?: boolean;
  error?: string;
  firstLinkName?: string;
  warningNote?: string;
}

export default function UploadSheetModal({
  isOpen,
  onClose,
  onSubmit,
  sampleFileUrl,
  title,
  acceptedFileTypes = ".xlsx,.xls,.csv",
  fullStockSheetUrl = "",
  loading = false,
  error,
  firstLinkName = "Download File",
  warningNote = "",
}: UploadSheetModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [localError, setLocalError] = useState<string>("");

  const handleDownload = async (url: string, filename: string) => {
    try {
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Download failed");
      }

      // Check if response is JSON (processing status) or file
      const contentType = response.headers.get("content-type");

      if (contentType && contentType.includes("application/json")) {
        // Handle JSON response (processing status)
        const data = await response.json();
        console.log("JSON response:", data);

        if (data.status === "processing") {
          // Show processing message instead of downloading
          setLocalError(""); // Clear any previous errors
          toast.success(
            data.message ||
              "File generation started. You will receive an email with the download link once it's ready."
          );
          onClose(); // Close the modal
          return;
        } else if (data.success === false) {
          // Handle error response
          throw new Error(data.message || "Request failed");
        }
      } else {
        // Handle file response (direct download)
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      }
    } catch (error) {
      console.error("Download error:", error);
      setLocalError("Failed to download file");
    }
  };

  if (!isOpen) return null;

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setLocalError("");
    }
  };

  const handleSubmit = () => {
    if (!selectedFile) {
      setLocalError("Please select a file to upload");
      return;
    }
    console.log("selectedFile", selectedFile);
    onSubmit(selectedFile);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        {/* Header */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-700"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="space-y-4">
            {warningNote !== "" && (
              <p className="text-red-600 text-sm">{warningNote}</p>
            )}
            <div className="flex-row justify-end">
              {/* Download Sample File */}
              {sampleFileUrl !== "" && (
                <div>
                  <button
                    onClick={() =>
                      handleDownload(
                        sampleFileUrl,
                        firstLinkName === "Download Orders Allocation Sheet"
                          ? `Order-Allocated-Sheet-${new Date().toDateString()}.xlsx`
                          : "Sample File.xlsx"
                      )
                    }
                    className="inline-flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <svg
                      className="h-5 w-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    {firstLinkName}
                  </button>
                </div>
              )}

              {/* Full Stock Sheet */}
              {fullStockSheetUrl !== "" && (
                <div>
                  <button
                    onClick={() =>
                      handleDownload(fullStockSheetUrl, "full_stock_file.xlsx")
                    }
                    className="inline-flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <svg
                      className="h-5 w-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    Download Full Stock File
                  </button>
                </div>
              )}
            </div>

            {/* File Upload */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                onChange={handleFileChange}
                accept={acceptedFileTypes}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center justify-center"
              >
                <svg
                  className="h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                  />
                </svg>
                <p className="mt-2 text-sm text-gray-600">
                  {selectedFile
                    ? selectedFile.name
                    : "Click to upload or drag and drop"}
                </p>
                <p className="mt-1 text-xs text-gray-500">
                  {acceptedFileTypes.replace(/,/g, ", ")} files supported
                </p>
              </label>
            </div>

            {(error || localError) && (
              <p className="text-red-600 text-sm">{error || localError}</p>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={onClose}
                disabled={loading}
                className={`px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 ${
                  loading ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                disabled={loading}
                className={`px-4 py-2 text-sm font-medium text-white bg-gray-700 rounded-md hover:opacity-90 flex items-center justify-center w-[85px] ${
                  loading ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                {loading ? (
                  <svg
                    className="animate-spin h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="4"
                      strokeLinecap="round"
                      d="M22 12a10 10 0 00-10-10"
                    />
                  </svg>
                ) : (
                  "Upload"
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
