import React, { useState, useEffect, useCallback, useRef } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import config from "../../../config.json";
import axios from "axios";

type UserGroup = {
  _id: string;
  name: string;
};

type FormData = {
  [key: string]: any;
};

type salesPerson = {
  _id: string;
  name: string;
};

export default function UpdatePeopleForm({
  editId,
  fetchPeopleData,
  setOpen,
  departmentList,
}: {
  editId: string;
  fetchPeopleData: any;
  setOpen: any;
  departmentList: any;
}) {
  const [formData, setFormData] = useState<FormData>({});
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
  const [error, setError] = useState("");
  const [selectedPseudoId, setSelectedPseudoId] = useState("");

  const [isOpen, setIsOpen] = useState(false);
  const [salesPerson, setSalesPerson] = useState<salesPerson[]>([]);
  const [selectedSalesPerson, setSelectedSalesPerson] = useState<any[]>([]);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const toggleDropdown = () => setIsOpen((prev) => !prev);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleCheckboxChange = (id: string) => {
    setSelectedSalesPerson((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((item) => item !== id)
        : [...prevSelected, id]
    );
  };

  const getSalesPersons = async () => {
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/department_people/all/salesperson`
      );
      console.log(response, "sales person");
      setSalesPerson(response.data?.data?.salesPersons);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getSalesPersons();
  }, []);

  // Fetch user groups from the API
  useEffect(() => {
    fetch(`${config.BACKEND_URL}/department_people`)
      .then((response) => {
        if (!response.ok) throw new Error("Failed to fetch user groups");
        return response.json();
      })
      .then((data) => {
        setUserGroups(data.data.data);
      })
      .catch((error) => console.error("Error fetching user groups:", error));
  }, []);

  const fetchData = useCallback(async (id: string) => {
    const response = await fetch(
      `${config.BACKEND_URL}/department_people/${id}`
    );
    if (!response.ok) throw new Error("Network response was not ok");
    const res = await response.json();
    delete res.data.data.password;

    let departmentType = res.data.data.departmentType;
    if (Array.isArray(departmentType) && departmentType.length > 0) {
      departmentType = departmentType[0];
    }
    setFormData({
      ...res.data.data,
      departmentType: departmentType ? { _id: departmentType._id } : { _id: "" }
    });
    return res;
  }, []);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["users", editId],
    queryFn: () => fetchData(editId),
  });

  // Initialize selectedPseudoId and selectedSalesPerson when formData is loaded
  useEffect(() => {
    if (formData?.departmentType?._id) {
      const selectedDepartment = departmentList.find(
        (dept: { id: string; pseudoId: string; name: string }) =>
          dept.id === formData.departmentType._id
      );
      setSelectedPseudoId(selectedDepartment?.pseudoId || "");
    }

    // Initialize selectedSalesPerson with existing data
    if (formData?.salesPersons && Array.isArray(formData.salesPersons)) {
      setSelectedSalesPerson(formData.salesPersons.map((sp: any) => sp._id || sp));
    }
  }, [formData, departmentList]);

  // Mutation for updating user details
  const updateUser = useMutation({
    mutationFn: (data: FormData) =>
      fetch(`${config.BACKEND_URL}/department_people/${editId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      }).then((response) => {
        response.json();
        setOpen(false);
        fetchPeopleData();
      }),
  });

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = event.target;
    if (name === "department") {
      setFormData((prev) => ({
        ...prev,
        departmentType: { _id: value },
      }));
      const selectedDepartment = departmentList.find(
        (dept: { id: string; pseudoId: string; name: string }) =>
          dept.id === value
      );
      setSelectedPseudoId(selectedDepartment?.pseudoId || "");
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log(selectedPseudoId, formData);
    if (
      !formData.name ||
      !formData.email ||
      !formData.departmentType?._id ||
      (selectedPseudoId === "SALES_PERSON" && !formData.salespersonkey)
    ) {
      setError(
        selectedPseudoId === "SALES_PERSON"
          ? "Please fill all fields, including the key for Sales Person."
          : "Please fill all fields."
      );
      return;
    }

    // Validation for SUNRISE_SALES_COORDINATOR department
    if (selectedPseudoId === "SUNRISE_SALES_COORDINATOR" && selectedSalesPerson.length === 0) {
      setError("Please select at least one Sales Person.");
      return;
    }

    // Prepare the data to be sent
    const submitData = {
      ...formData,
      salesPersons: selectedPseudoId === "SUNRISE_SALES_COORDINATOR" ? selectedSalesPerson : undefined
    };

    updateUser.mutate(submitData);
  };

  // useEffect(() => {
  //   document.addEventListener('mousedown', handleClickOutside);
  //   return () => {
  //     document.removeEventListener('mousedown', handleClickOutside);
  //   };
  // }, []);

  return (
    <form onSubmit={handleSubmit}>
      <div className="pb-6">
        <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Email
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="email"
              name="email"
              value={formData.email || ""}
              disabled
              onChange={handleInputChange}
            />
          </div>
          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Name
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="text"
              name="name"
              value={formData.name || ""}
              onChange={handleInputChange}
            />
          </div>
          <div className="sm:col-span-3">
            <label
              htmlFor="department"
              className="block text-sm font-medium leading-6 text-gray-900"
            >
              Department
            </label>
            <select
              id="department"
              name="department"
              className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
              value={formData?.departmentType?._id || ""}
              onChange={handleInputChange}
            >
              <option value="">Select Department</option>
              {departmentList.map((x: any) => (
                <option className="text-black" value={x.id} key={x.id}>
                  {x.name}
                </option>
              ))}
            </select>
          </div>

          {selectedPseudoId === "SALES_PERSON" && (
            <div className="relative">
              <label
                htmlFor="salespersonkey"
                className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
              >
                Key
              </label>
              <input
                type="text"
                name="salespersonkey"
                id="salespersonkey"
                value={formData.salespersonkey || ""}
                onChange={handleInputChange}
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              />
            </div>
          )}

          <div className="relative w-full">
            {selectedPseudoId === "SUNRISE_SALES_COORDINATOR" && (
              <div className="relative w-full" ref={dropdownRef}>
                <div
                  onClick={toggleDropdown}
                  className="cursor-pointer w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-sm text-gray-900"
                >
                  {selectedSalesPerson.length > 0
                    ? salesPerson
                        .filter((dept) =>
                          selectedSalesPerson.includes(dept._id)
                        )
                        .map((dept) => dept.name)
                        .join(", ")
                    : "Select Sales Person"}
                </div>

                {isOpen && (
                  <div className="z-20 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
                    {salesPerson.map((dept) => (
                      <label
                        key={dept._id}
                        className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer text-black"
                      >
                        <input
                          type="checkbox"
                          className="mr-2"
                          checked={selectedSalesPerson.includes(dept._id)}
                          onChange={() => handleCheckboxChange(dept._id)}
                        />
                        {dept.name}
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Password
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="password"
              name="password"
              value="********"
              disabled
            />
          </div>
          <div className="sm:col-span-3">
            {error && <p className="text-red-600 font-bold">{error}</p>}
          </div>

          <div className="mt-6 flex justify-center items-center gap-x-6">
            <button
              type="button"
              className="text-sm w-[30%] font-semibold leading-6 text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
              onClick={() => setOpen(false)}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="rounded-md w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
            >
              Update User
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
