"use client";
import React, {
  useEffect,
  useState,
  Fragment,
  useCallback,
  useRef,
} from "react";
import Sidebar from "../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import axios from "axios";
import config from "../../../config.json";
import currency from "../../../Currency.json";
import {
  Dialog,
  Disclosure,
  Menu,
  Popover,
  Transition,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { ChevronDownIcon, ArrowPathIcon } from "@heroicons/react/20/solid";
import ConfirmationModal from "../components/confirmationModal";
import Pagination from "../components/pagination";
import Cookies from "js-cookie";
import { useRouter } from "next/navigation";
import { useDebounce } from "../../../src/hooks/useDebounce";
import { ImSpinner8 } from "react-icons/im";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { TableRowsWithData } from "../components/tableBody";
import { StatusCards } from "../components/statusCards";
import UploadSheetModal from "../components/UploadSheetModal";
import ErrorNotification from "../components/ErrorNotification";
import SuccessNotification from "../components/SuccessNotifications";
import { toast, ToastContainer } from "react-toastify";

const queryClient = new QueryClient();
export default function Orders() {
  const [open, setOpen] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [activeFilters, setActiveFilters]: any = useState([]);
  const [filters, setFilters]: any = useState([]);
  const [status, setStatus] = useState([]);
  const [fromDate, setFromDate]: any = useState();
  const [toDate, setToDate]: any = useState();
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false);
  const [paginationCount, setPaginationCount]: any = useState(0);
  const limit = 25;
  const [alreadyAppliedFilters, setAlreadyAppliedFilters]: any = useState({
    status: "",
  });
  const [loader, setLoader]: any = useState(false);
  const [filterValue, setFilterValue]: any = useState([]);
  const [userRole, setUserRole]: any = useState("");

  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const debouncedValue = useDebounce(searchQuery, 500);
  const searchInputRef = useRef(null);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [activeTab, setActiveTab] = useState("All");

  const tabs = [
    { name: "All", value: "" },
    { name: "Created", value: "created" },
    { name: "Pending", value: "pending" },
    { name: "Rejected", value: "rejected" },
    { name: "Order Placed", value: "order placed" },
    { name: "Invoiced", value: "invoiced" },
    { name: "On Hold", value: "nn hold" },
    { name: "Out For Delivery", value: "out for delivery" },
    { name: "Order Completed", value: "order completed" },
    { name: "Order Deleted", value: "order deleted" },
  ];

  const filterSelected = (option: any) => {
    let arr = [option.value];
    setActiveFilters((activeFilters: any) => ({
      ...activeFilters,
      ...arr,
    }));
  };
  const removeFilter = (label: any) => {};
  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  const router = useRouter();
  async function fetchOrderData(page) {
    setLoader(true);
    try {
      let response = await axios.request({
        method: "get",
        url: `${config.BACKEND_URL}/order?page=${page}&limit=${limit}`,
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
          "Content-Type": "application/json",
        },
      });

      const totalCount = Number(response.data.result);
      const numberOfPages = totalCount / limit;
      setPaginationCount(numberOfPages);

      let data = response.data.data.data.map((x: any) => {
        return {
          date: new Date(x.createdAt).toString().slice(0, 15),
          orderID: x._id,
          shopifyOrderNumber: x.name,
          omsOrderNumber: x.subOrderId,
          status: x.status,
          colorCode: x.status?.colorCode,
          budgetCategory: x.type === "pre-order" ? "-" : x.budgetCategoryValue,
          preOrderNumber: x.type === "pre-order" ? x.preOrderValue : "-",
          quantity: x.line_items.length,
          paymentStatus: x.financial_status,
          amount: calculateTotalPrice(x.line_items),
          currency: currency[x.shipping_address?.country],
          pseudoId: x.status.pseudoId,
          isFuture: x.isFuture,
          SageOrderNumber: x.sageOrderNumber,
          totalLineItems: x.line_items.length,
          ecpacStatus: x.sageStatus,
        };
      });

      console.log(response.data.data.data, "all data here ");

      setTableData(data);
      setFilters([]);

      setTimeout(() => {
        setRefreshButtonClicked(false);
      }, 2000);
    } catch (err) {
      console.log("error in fetching order data", err);
    } finally {
      setLoader(false);
    }
  }

  function calculateTotalPrice(items) {
    let orderTotalPrice = items.reduce((value, total) => {
      let totalPrice = total.price * total.quantity;
      value += totalPrice;
      return value;
    }, 0);
    return orderTotalPrice.toFixed(2);
  }

  useEffect(() => {
    if (fromDate || toDate) searchBarInput(1);
  }, [fromDate, toDate]);

  const [isInitialRender, setIsInitialRender] = useState(true);
  useEffect(() => {
    if (!isInitialRender) {
      searchBarInput(1);
    } else {
      setIsInitialRender(false);
    }
  }, [alreadyAppliedFilters]);

  async function searchBarInput(page) {
    setLoader(true);
    try {
      const myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      myHeaders.append("Authorization", `Bearer ${Cookies.get("token")}`);

      const raw = JSON.stringify({
        input: alreadyAppliedFilters.input,
        fromDate: alreadyAppliedFilters.fromDate,
        toDate: alreadyAppliedFilters.toDate,
        budgetCategoryValue: alreadyAppliedFilters.budgetCategoryValue,
        preOrderValue: alreadyAppliedFilters.preOrderValue,
        pageSize: limit,
        page: page,
        status: alreadyAppliedFilters.status,
      });

      if (alreadyAppliedFilters || alreadyAppliedFilters.length > 0) {
        Cookies.set("orderFilter", raw);
      }

      const requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: Cookies.get("orderFilter") || raw,
      };

      const responsePromise = await fetch(
        // `${config.BACKEND_URL}/search`,
        `${config.BACKEND_URL}/search`,
        requestOptions
      );
      const response = await responsePromise.json();
      const totalCount = Number(response.total);
      const numberOfPages = totalCount / limit;
      setPaginationCount(numberOfPages);

      let data = response.orders.map((x: any) => {
        return {
          date: new Date(x.createdAt).toString().slice(0, 15),
          orderID: x._id,
          shopifyOrderNumber: x.name,
          omsOrderNumber: x.subOrderId,
          status: x?.status,
          colorCode: x.status?.colorCode,
          budgetCategory: x.type === "pre-order" ? "-" : x.budgetCategoryValue,
          preOrderNumber: x.type === "pre-order" ? x.preOrderValue : "-",
          quantity: x.line_items.length,
          amount: calculateTotalPrice(x.line_items),
          currency: currency[x.shipping_address?.country],
          distributorName: `${x.distributor?.name}`,
          isFuture: x.isFuture,
          SageOrderNumber: x.sageOrderNumber,
          totalLineItems: x.line_items.length,
          ecpacStatus: x.sageStatus,
        };
      });

      setTableData(data);
      setRefreshButtonClicked(false);
    } catch (err) {
      console.log("Error in seachBarInput", err);
      setRefreshButtonClicked(false);
    } finally {
      setLoader(false);
      setRefreshButtonClicked(false);
      setIsSearching(false);
    }
  }

  async function fetchOrderStatus() {
    setLoader(true);
    try {
      let response = await axios.request({
        method: "get",
        url: `${config.BACKEND_URL}/order/order-by-status`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });

      let orderCount = response.data.data.total || 0;
      let data = response.data.data.data.map((x: any) => ({
        type: x.status || x.sageStatus,
        count: x.count,
      }));
      let initialData: any = { type: "Total Orders", count: orderCount };
      let finalData: any = [initialData].concat(data);
      setStatus(finalData);
    } catch (err) {
      console.log("Error in fetching order status", err);
    } finally {
      setLoader(false);
    }
  }

  const [bulkProcessLoading, setBulkProcessLoading]: any = useState(false);
  const [bulkProcessError, setBulkProcessError]: any = useState(null);

  useEffect(() => {
    // fetchOrderData(1);
    // fetchOrderStatus();

    const savedFilters = Cookies.get("orderFilter");
    if (savedFilters) {
      // setAlreadyAppliedFilters(JSON.parse(savedFilters));
      Cookies.remove("orderFilter");
    }
    setUserRole(Cookies.get("userrole"));
  }, []);

  useEffect(() => {
    let timeout;
    if (bulkProcessError) {
      const error = bulkProcessError.split("@@");
      timeout = setTimeout(() => {
        setBulkProcessError(null);
      }, 7000);
    }
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [bulkProcessError]);

  const handlePageChange = (page) => {
    searchBarInput(page);
  };

  const handleCheckbox = (
    e: React.ChangeEvent<HTMLInputElement>,
    id: string
  ) => {
    if (e.target.checked) {
      setSelectedOrders((prevSelectedIds) => [...prevSelectedIds, id]);
    } else {
      setSelectedOrders((prevSelectedIds) =>
        prevSelectedIds.filter((selectedId) => selectedId !== id)
      );
    }
  };

  const getFilterValues = async () => {
    try {
      let response = await axios.request({
        method: "get",
        url: `${config.BACKEND_URL}/order/filter-values`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });
      console.log(response, "response here");
      setFilterValue(response.data.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getFilterValues();
  }, []);

  const handleSearchOrder = useCallback((e) => {
    setIsSearching(true);
    setSearchQuery(e.target.value);
  }, []);

  useEffect(() => {
    if (debouncedValue) {
      setAlreadyAppliedFilters((prev) => ({ ...prev, input: debouncedValue }));
    }
  }, [debouncedValue]);

  useEffect(() => {
    if (!searchQuery) {
      setIsSearching(false);
      fetchOrderData(1);
      fetchOrderStatus();
    }
  }, [searchQuery]);

  const handleFileUpload = async (file: File) => {
    setBulkProcessLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      const response = await fetch(
        `${config.BACKEND_URL}/action/autoAllocate`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          body: formData,
        }
      );

      // Check if response is a file (Excel/CSV) by checking content-type
      const contentType = response.headers.get("content-type");
      const contentDisposition = response.headers.get("content-disposition");

      if (
        (contentType &&
          (contentType.includes(
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          ) ||
            contentType.includes("application/vnd.ms-excel") ||
            contentType.includes("text/csv") ||
            contentType.includes("application/octet-stream"))) ||
        (contentDisposition && contentDisposition.includes("attachment"))
      ) {
        setErrorMessage(
          "File processing failed. Downloading an error sheet with details in 3 seconds..."
        );
        setShowErrorNotification(true);

        // Response is a file, prepare for download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;

        // Extract filename from content-disposition header or use default
        let filename = "error_report.xlsx";
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(
            /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
          );
          if (filenameMatch && filenameMatch[1]) {
            filename = filenameMatch[1].replace(/['"]/g, "");
          }
        }

        link.download = filename;

        setBulkProcessLoading(false);
        setConfirmationModalOpen(false);
        setBulkProcessError(null);

        // Wait 3 seconds before starting download
        setTimeout(() => {
          setErrorMessage("Downloading error sheet now...");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          // Hide notification after download starts
          setTimeout(() => {
            setShowErrorNotification(false);
          }, 2000);
        }, 3000);

        return;
      }

      // Response is JSON, handle normally
      const data = await response.json();
      if (data.status === "success") {
        setConfirmationModalOpen(false);
        setBulkProcessLoading(false);
        setBulkProcessError(null);
        setSuccessMessage("File uploaded successfully!");
        setShowSuccessNotification(true);
        setSelectedOrders([]); // Unselect all orders after successful upload
        fetchOrderData(1);
        fetchOrderStatus();

        // Hide success notification after 5 seconds
        setTimeout(() => {
          setShowSuccessNotification(false);
        }, 5000);
      } else {
        setBulkProcessLoading(false);
        setBulkProcessError(data.message);
        setErrorMessage(data.message || "File upload failed");
        setShowErrorNotification(true);

        // Hide error notification after 5 seconds
        setTimeout(() => {
          setShowErrorNotification(false);
        }, 5000);
      }
    } catch (error) {
      setBulkProcessLoading(false);
      setBulkProcessError("An error occurred during file upload.");
      setErrorMessage("An error occurred during file upload.");
      setShowErrorNotification(true);

      setTimeout(() => {
        setShowErrorNotification(false);
      }, 5000);
    }
  };

  // Toast notification effect for success
  useEffect(() => {
    if (showSuccessNotification) {
      toast.success(`${successMessage}`);
    }
  }, [showSuccessNotification, successMessage]);

  // Toast notification effect for error
  useEffect(() => {
    if (showErrorNotification) {
      toast.error(`${errorMessage}`);
    }
  }, [showErrorNotification, errorMessage]);

  const buildQueryParams = (filters) => {
    const params = new URLSearchParams();

    if (filters.input) params.append("input", filters.input);
    if (filters.fromDate) params.append("fromDate", filters.fromDate);
    if (filters.toDate) params.append("toDate", filters.toDate);
    if (filters.budgetCategoryValue)
      params.append("budgetCategoryValue", filters.budgetCategoryValue);
    if (filters.preOrderValue)
      params.append("preOrderValue", filters.preOrderValue);
    if (filters.status) params.append("status", filters.status);

    return params.toString();
  };

  const queryParams = buildQueryParams(alreadyAppliedFilters);

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        {
          <>
            <UploadSheetModal
              isOpen={confirmationModalOpen}
              onClose={() => setConfirmationModalOpen(false)}
              onSubmit={handleFileUpload}
              sampleFileUrl={
                selectedOrders.length > 0
                  ? `${
                      config.BACKEND_URL
                    }/order/pending/orders?orderIds=${selectedOrders.join(
                      ","
                    )}&${queryParams}`
                  : `${config.BACKEND_URL}/order/pending/orders?${queryParams}`
              }
              title="Upload Sheet"
              acceptedFileTypes=".xlsx,.xls,.csv"
              fullStockSheetUrl=""
              loading={bulkProcessLoading}
              firstLinkName="Download Orders Allocation Sheet"
            />
            <div className="flex p-4 items-center">
              <h2 className="text-3xl w-[95%] font-semibold leading-6 text-gray-900">
                Order
              </h2>
              <div className="flex p-4 items-center justify-end">
                <button
                  type="button"
                  className="flex items-center justify-center rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 w-12 h-12"
                  onClick={() => {
                    setRefreshButtonClicked(true);
                    if (alreadyAppliedFilters) {
                      searchBarInput(1);
                    } else {
                      fetchOrderData(1);
                      setRefreshButtonClicked(false);
                    }
                    fetchOrderStatus();
                  }}
                >
                  <ArrowPathIcon
                    className={`${
                      refreshButtonClicked ? "animate-spin" : "animate-none"
                    } h-5 w-10`}
                  />
                </button>
              </div>
            </div>

            <div className="bg-white-900 pt-4 pb-6">
              <dl className=" grid grid-cols-1 gap-0.5 overflow-hidden rounded-2xl text-center sm:grid-cols-2 lg:grid-cols-6 min-h-[100px]">
                {status.length === 0 ? (
                  <div>
                    <Skeleton count={4} highlightColor="gray"></Skeleton>
                  </div>
                ) : (
                  <StatusCards status={status} />
                )}
              </dl>
            </div>

            {/* <div className="flex justify-end px-9">

          <button
                  type="button"
                  className="flex justify-around w-[5%] rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  onClick={() => fetchOrderData()}>
                 <ArrowPathIcon className={classNames("h-5 w-5 transform")} />
                </button>
       </div> */}
            <div className="bg-white px-4 rounded-lg">
              <Transition.Root show={open} as={Fragment}>
                <Dialog className="relative z-40 sm:hidden" onClose={setOpen}>
                  <Transition.Child
                    as={Fragment}
                    enter="transition-opacity ease-linear duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="transition-opacity ease-linear duration-300"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                  >
                    <div className="fixed inset-0 bg-black bg-opacity-25" />
                  </Transition.Child>
                  <div className="fixed inset-0 z-40 flex">
                    <Transition.Child
                      as={Fragment}
                      enter="transition ease-in-out duration-300 transform"
                      enterFrom="translate-x-full"
                      enterTo="translate-x-0"
                      leave="transition ease-in-out duration-300 transform"
                      leaveFrom="translate-x-0"
                      leaveTo="translate-x-full"
                    >
                      <Dialog.Panel className="relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-12 shadow-xl">
                        <div className="flex items-center justify-between px-4">
                          <button
                            type="button"
                            className="-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400"
                            onClick={() => setOpen(false)}
                          >
                            <span className="sr-only">Close menu</span>
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                          </button>
                        </div>
                        <form className="mt-4">
                          {filters.map((section) => (
                            <Disclosure
                              as="div"
                              key={section.name}
                              className="border-t border-gray-200 px-4 py-6"
                            >
                              {({ open }) => (
                                <>
                                  <h3 className="-mx-2 -my-3 flow-root">
                                    <Disclosure.Button className="flex w-full items-center justify-between bg-white px-2 py-3 text-sm text-gray-400">
                                      <span className="font-medium text-gray-900">
                                        {section.name}
                                      </span>
                                      <span className="ml-6 flex items-center">
                                        <ChevronDownIcon
                                          className={classNames(
                                            open ? "-rotate-180" : "rotate-0",
                                            "h-5 w-5 transform"
                                          )}
                                          aria-hidden="true"
                                        />
                                      </span>
                                    </Disclosure.Button>
                                  </h3>
                                  <Disclosure.Panel className="pt-6">
                                    <div className="space-y-6">
                                      {section.options.map(
                                        (option: any, optionIdx: any) => (
                                          <div
                                            key={option.value}
                                            className="flex items-center"
                                          >
                                            <input
                                              id={`filter-mobile-${section.id}-${optionIdx}`}
                                              name={`${section.id}[]`}
                                              defaultValue={option.value}
                                              type="checkbox"
                                              defaultChecked={option.checked}
                                              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                            />
                                            <label
                                              htmlFor={`filter-mobile-${section.id}-${optionIdx}`}
                                              className="ml-3 text-sm text-[#222222]"
                                            >
                                              {option.label}
                                            </label>
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </Disclosure.Panel>
                                </>
                              )}
                            </Disclosure>
                          ))}
                        </form>
                      </Dialog.Panel>
                    </Transition.Child>
                  </div>
                </Dialog>
              </Transition.Root>
              <section aria-labelledby="filter-heading">
                <div className="border-b border-gray-200 bg-white rounded-lg pb-4">
                  {/* // Commented to hide bulk process errors 
              {bulkProcessError != null &&
                showBulkProcessError &&
                showBulkProcessError.length > 0 &&
                showBulkProcessError.map((line: any, index: any) => {
                  return (
                    <p
                      className="text-red-600 text-right font-bold"
                      key={`${index}-${line}`}
                    >
                      {line}
                    </p>
                  );
                })} */}

                  <div className="mx-auto flex flex-row w-full gap-4 px-4 sm:px-6 lg:px-0">
                    <div className="flex-1 flex flex-col justify-end">
                      <label>Search</label>
                      <input
                        ref={searchInputRef}
                        type="text"
                        name="email"
                        id="searchBarInput"
                        className=" relative w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        placeholder="Order Name"
                        value={searchQuery}
                        onChange={handleSearchOrder}
                      />
                      {isSearching && (
                        <span className="absolute right-2 transform translate-y-1/2">
                          <ImSpinner8
                            fill="black"
                            size={20}
                            className={`$ {
                              isSearching ? "animate-spin" : "animate-none"
                            }`}
                          />
                        </span>
                      )}
                    </div>
                    <div className="flex-1 flex flex-row gap-3 items-end justify-end">
                      <div className="mt-2">
                        <label
                          htmlFor="from_date"
                          className="block text-sm font-medium text-gray-700"
                        >
                          From
                        </label>
                        <input
                          type="date"
                          id="from_date"
                          value={alreadyAppliedFilters?.fromDate}
                          onChange={(e) =>
                            setAlreadyAppliedFilters((prev) => ({
                              ...prev,
                              fromDate: e.target.value,
                            }))
                          }
                          className="block w-full rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                      <div className="mt-2">
                        <label
                          htmlFor="to_date"
                          className="block text-sm font-medium text-gray-700"
                        >
                          To
                        </label>
                        <input
                          type="date"
                          id="to_date"
                          value={alreadyAppliedFilters?.toDate}
                          onChange={(e) =>
                            setAlreadyAppliedFilters((prev) => ({
                              ...prev,
                              toDate: e.target.value,
                            }))
                          }
                          className="block w-full rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                      <div className="mt-2 max-w-[200px]">
                        <label
                          htmlFor="budgetCategory"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Budget Category
                        </label>
                        <select
                          id="budgetCategory"
                          value={
                            alreadyAppliedFilters?.budgetCategoryValue || ""
                          }
                          onChange={(e) =>
                            setAlreadyAppliedFilters((prev) => ({
                              ...prev,
                              budgetCategoryValue: e.target.value,
                            }))
                          }
                          className="block w-full rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm"
                        >
                          <option value="">All</option>
                          {filterValue?.budgetCategoryValues?.map(
                            (item: any) => (
                              <option
                                key={item}
                                value={item}
                                className="text-gray-900"
                              >
                                {item}
                              </option>
                            )
                          )}
                        </select>
                      </div>
                      <div className="mt-2 w-[200px] max-w-[200px]">
                        <label
                          htmlFor="preOrder"
                          className="block text-sm font-medium text-gray-700"
                        >
                          Pre Order
                        </label>
                        <select
                          id="preOrder"
                          value={alreadyAppliedFilters?.preOrderValue || ""}
                          onChange={(e) =>
                            setAlreadyAppliedFilters((prev) => ({
                              ...prev,
                              preOrderValue: e.target.value,
                            }))
                          }
                          className="block w-full rounded-md border-0 py-1.5 px-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm"
                        >
                          <option value="">All</option>
                          {filterValue?.preOrderValues?.map((item: any) => (
                            <option
                              key={item}
                              value={item}
                              className="text-gray-900"
                            >
                              {item}
                            </option>
                          ))}
                        </select>
                      </div>
                      {(userRole === "Sunrise Admin" ||
                        userRole === "Finance Manager" ||
                        userRole === "Finance Executive" ||
                        userRole === "Sales Person") && (
                        <div className="relative mt-auto mx-1 inline-block">
                          <button
                            onClick={() => {
                              setConfirmationModalOpen(true);
                            }}
                            type="button"
                            disabled={
                              bulkProcessLoading ||
                              !["created", "pending", ""].includes(
                                alreadyAppliedFilters.status
                              )
                            }
                            className={`rounded-md whitespace-nowrap px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
                              bulkProcessLoading
                                ? "bg-gray-400 cursor-not-allowed"
                                : "bg-gray-700"
                            }`}
                          >
                            Bulk Process
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {activeFilters.length > 0 ? (
                  <div className="bg-gray-100">
                    <div className="mx-auto px-4 py-3 sm:px-6 lg:px-8">
                      <div className="mt-2 sm:ml-4 sm:mt-0">
                        <div className="-m-1 flex flex-wrap items-center">
                          {activeFilters.map((activeFilter: any) => (
                            <span
                              key={activeFilter.value}
                              className="m-1 inline-flex items-center rounded-full border border-gray-200 bg-white py-1.5 pl-3 pr-2 text-sm font-medium text-gray-900"
                            >
                              <span>{activeFilter.label}</span>
                              <button
                                type="button"
                                onClick={() => {
                                  removeFilter(activeFilter.label);
                                }}
                                className="ml-1 inline-flex h-4 w-4 flex-shrink-0 rounded-full p-1 text-gray-400 hover:bg-gray-200 hover:text-[#222222]"
                              >
                                <span className="sr-only">
                                  Remove filter for {activeFilter.label}
                                </span>
                                <svg
                                  className="h-2 w-2"
                                  stroke="currentColor"
                                  fill="none"
                                  viewBox="0 0 8 8"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeWidth="1.5"
                                    d="M1 1l6 6m0-6L1 7"
                                  />
                                </svg>
                              </button>
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div></div>
                )}
              </section>
            </div>

            {/* Status Tabs */}
            <div className="mt-10">
              <div className="w-full bg-white rounded-lg shadow-sm border border-gray-200 p-1 flex">
                {tabs.map((tab) => (
                  <button
                    key={tab.name}
                    onClick={() => {
                      setActiveTab(tab.name);
                      setAlreadyAppliedFilters((prev) => ({
                        ...prev,
                        status: tab.value.toLowerCase(),
                      }));
                    }}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.name
                        ? "bg-[#222222] text-white shadow-sm"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                    }`}
                  >
                    {tab.name}
                  </button>
                ))}
              </div>
            </div>

            <div className="">
              <div className="mt-8 flow-root">
                <div className="-mx-4 -my-2 overflow-x-auto">
                  <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <table className="table-fixed rounded-lg w-full text-center shadow-sm divide-y divide-gray-300">
                      <thead className=" bg-[#222222] rounded-tl-lg rounded-tr-lg w-fit">
                        <tr className="h-[50px] divide-x divide-gray">
                          <th className="w-[50px]"></th>
                          <th
                            scope="col"
                            className="whitespace-nowrap text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Shopify Order No.
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px]  text-center"
                          >
                            OMS Order No.
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Order amount
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Budget Category
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Pre Order No.
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px] text-center"
                          >
                            No. of Items
                          </th>
                          <th
                            scope="col"
                            className="whitespace-nowrap text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Is Future
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Sage Order No.
                          </th>
                          <th
                            scope="col"
                            className="text-sm font-semibold text-white w-[150px] text-center"
                          >
                            Status
                          </th>
                        </tr>
                      </thead>

                      <tbody className="relative bg-white w-[200px]">
                        {loader ? (
                          <tr>
                            <td colSpan={10} className="py-6 text-center">
                              <ImSpinner8
                                size={30}
                                fill="black"
                                className="animate-spin inline-block"
                              />
                            </td>
                          </tr>
                        ) : (
                          <TableRowsWithData
                            tableData={tableData}
                            selectedOrders={selectedOrders}
                            handleCheckbox={handleCheckbox}
                            router={router}
                          />
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              {paginationCount > 1 && (
                <Pagination
                  paginationCount={Math.ceil(paginationCount)}
                  onPageChange={handlePageChange}
                />
              )}
            </div>
          </>
        }
      </Sidebar>
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </QueryClientProvider>
  );
}
