"use client";
import React, {
  useEffect,
  useState,
  Fragment,
  useRef,
  useCallback,
} from "react";
import Sidebar from "../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../../config.json";
import {
  EllipsisVerticalIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { DeleteConfirmationModal } from "@/app/components/deleteConfirmationModal";
import { useDrag, useDrop, DndProvider, XYCoord } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import UpdateDesignationModal from "@/app/components/updateDesignationModal";
import Cookies from "js-cookie";

const queryClient = new QueryClient();

const ItemType = "row";

export default function Designation() {
  const [designationTable, setDesignationsTable] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);
  const [error, setError] = useState<String>();
  const cancelButtonRef = useRef(null);
  const [editId, setEditId] = useState("");

  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [newScopes, setNewScopes]: any = useState({});
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [countryManager, setCountryManager] = useState("false");

  useEffect(() => {
    const scopes = Cookies.get("type");
    const getScopes = scopes && JSON.parse(scopes);
    setNewScopes(getScopes);
  }, []);

  const DraggableTableRow = ({ data, index, moveRow }) => {
    const ref = useRef<HTMLTableRowElement>(null);
    const [, drop] = useDrop({
      accept: ItemType,
      hover(item: { type: string; index: number }, monitor) {
        if (!ref.current) {
          return;
        }
        const dragIndex = item.index;
        const hoverIndex = index;

        if (dragIndex === hoverIndex) {
          return;
        }

        const hoverBoundingRect = ref.current.getBoundingClientRect();
        const hoverMiddleY =
          (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
        const clientOffset = monitor.getClientOffset() as XYCoord;
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;

        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return;
        }
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return;
        }

        moveRow(dragIndex, hoverIndex);

        item.index = hoverIndex;
      },
    });

    const [{ isDragging }, drag] = useDrag({
      type: ItemType,
      item: () => {
        return { type: ItemType, id: data.id, index };
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    drag(drop(ref));

    return (
      <tr
        ref={ref}
        key={data.id}
        style={{ opacity: isDragging ? 0 : 1 }}
        className="even:bg-gray-50 cursor-all-scroll"
      >
        <td className="w-6 items-center h-[4.25rem] bg-[#80808030] flex">
          <EllipsisVerticalIcon className="w-full ml-auto text-gray-400" />
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.hierarchy + 1}
        </td>
        <td className="hitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.name}
        </td>
        {newScopes && newScopes?.designation?.length > 0 && (
          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3 flex flex-row">
            {newScopes?.designation?.includes("write") && (
              <button
                onClick={() => {
                  setUpdateModalOpen(true);
                  setEditId(data?.id);
                }}
                type="button"
                className="block rounded-full bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                <PencilIcon className="h-5 w-5" aria-hidden="true" />
              </button>
            )}
            {newScopes?.designation?.includes("delete") && (
              <button
                onClick={() => deleteDesignation(data.id)}
                type="button"
                className="block rounded-full ml-4 bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                <TrashIcon className="h-5 w-5" aria-hidden="true" />
              </button>
            )}
          </td>
        )}
      </tr>
    );
  };

  function deleteDesignation(id: any) {
    setCurrentId(id);
    setCurrentType("designation");
    setConfirmationOpen(true);
  }

  async function fetchDesignationTable() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/designation`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        hierarchy: x.hierarchy,
        id: x._id,
        name: x.name,
      };
    });
    return data;
  }

  async function sendDesignationCreationData() {
    const nameValue = (document.getElementById("name") as HTMLFormElement).value;

    if (!nameValue.trim()) {
      setError("Please fill in all the fields.");
      return;
    }

    const result = await axios.request({
      method: "post",
      url: `${config.BACKEND_URL}/designation`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        hierarchy: designationTable.length,
        name: nameValue,
        isCountryManager: countryManager === "true",
      }),
    });

    setError("");
    setOpen(false);
    const data = await fetchDesignationTable();
    setDesignationsTable(data);
  }

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchDesignationTable();
      setDesignationsTable(data);
    };

    fetchData();
  }, [open]);

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const newData = [...designationTable];
      const [draggedItem] = newData.splice(dragIndex, 1);
      newData.splice(hoverIndex, 0, draggedItem);
      newData.forEach((item, index) => {
        item.hierarchy = index;
      });

      setDesignationsTable(newData);
      updatePriorityList(newData);
    },
    [designationTable]
  );

  async function updatePriorityList(updatedData) {
    const filterDistributorData = updatedData.map((data) => {
      return { _id: data.id, hierarchy: data.hierarchy };
    });
    let response = await axios.request({
      method: "PATCH",
      url: `${config.BACKEND_URL}/designation/hierarchy/update`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({ designations: filterDistributorData }),
    });

    await fetchDesignationTable();
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            className="relative z-10"
            initialFocus={cancelButtonRef}
            onClose={setOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div className="flex justify-end">
                      <XMarkIcon
                        onClick={() => setOpen(false)}
                        className="cursor-pointer h-6 w-6 text-black"
                      />
                    </div>
                    <div>
                      <div className="mt-3 text-center sm:mt-5">
                        <div className="isolate -space-y-px rounded-md shadow-sm">
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Name
                            </label>
                            <input
                              type="text"
                              name="name"
                              id="name"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                        </div>
                      </div>
                      <div className="mt-3 sm:mt-5">
                        <p className="mt-1 text-sm leading-6 text-gray-600">
                          Is it country manager?
                        </p>
                        <div className="mt-6 space-y-6 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
                          <div className="flex items-center">
                            <input
                              id="countryManagerYes"
                              name="countryManager"
                              type="radio"
                              value="true"
                              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                              checked={countryManager === "true"}
                              onChange={() => setCountryManager("true")}
                            />
                            <label
                              htmlFor="countryManagerYes"
                              className="ml-3 block text-sm font-medium leading-6 text-gray-900"
                            >
                              Yes
                            </label>
                          </div>
                          <div className="flex items-center">
                            <input
                              id="countryManagerNo"
                              name="countryManager"
                              type="radio"
                              value="false"
                              className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
                              checked={countryManager === "false"}
                              onChange={() => setCountryManager("false")}
                            />
                            <label
                              htmlFor="countryManagerNo"
                              className="ml-3 block text-sm font-medium leading-6 text-gray-900"
                            >
                              No
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 sm:mt-6 border-t-2 flex justify-center sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-[30%] mt-4 justify-center rounded-md text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2"
                        onClick={() => {
                          setOpen(false);
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="inline-flex w-[30%] mt-4 justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2"
                        onClick={() => {
                          sendDesignationCreationData();
                          setOpen(false);
                        }}
                      >
                        Create
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>

        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            {error && <p className="text-red-600 font-bold">{error}</p>}
            <div className="sm:flex-auto"></div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                onClick={() => setOpen(true)}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Add Designation
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <DndProvider backend={HTML5Backend}>
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th scope="col" className="rounded-tl-lg"></th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Hierarchy
                        </th>
                        <th
                          scope="col"
                          className={`${
                            (newScopes &&
                              newScopes?.designation?.length > 0 &&
                              newScopes?.designation?.includes("write")) ||
                            newScopes?.designation?.includes("delete")
                              ? ""
                              : "rounded-tr-lg"
                          } px-3 py-3.5 text-left text-sm font-semibold text-gray-900`}
                        >
                          Name
                        </th>
                        {(newScopes &&
                          newScopes?.designation?.length > 0 &&
                          newScopes?.designation?.includes("write")) ||
                        newScopes?.designation?.includes("delete") ? (
                          <th
                            scope="col"
                            className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Action
                          </th>
                        ) : (
                          ""
                        )}
                      </tr>
                    </thead>

                    <tbody className="bg-white">
                      {designationTable
                        .sort((a, b) => a.hierarchy - b.hierarchy)
                        .map((data, index) => (
                          <DraggableTableRow
                            key={data.id}
                            index={index}
                            data={data}
                            moveRow={moveRow}
                          />
                        ))}
                    </tbody>
                  </table>
                </DndProvider>
              </div>
            </div>
          </div>
        </div>

        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchDesignationTable}
            setTableData={setDesignationsTable}
          />
        )}
        {updateModalOpen && (
          <UpdateDesignationModal
            editId={editId}
            open={updateModalOpen}
            fetchDesignationTable={fetchDesignationTable}
            setOpen={setUpdateModalOpen}
            setDesignationsTable={setDesignationsTable}
          />
        )}
      </Sidebar>
    </QueryClientProvider>
  );
}
