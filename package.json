{"name": "titan-d2d-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start --port 3000", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.3", "@tailwindcss/forms": "^0.5.7", "@tanstack/react-query": "^5.29.2", "axios": "^1.6.8", "cookies": "^0.9.1", "dotenv": "^16.4.5", "js-cookie": "^3.0.5", "next": "14.2.0", "react": "^18", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-icons": "^5.5.0", "react-loading-skeleton": "^3.5.0", "react-multi-select-component": "^4.3.4", "react-toastify": "^11.0.5", "recharts": "^2.12.6", "sharp": "^0.33.3", "styled-components": "^6.1.18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}