"use client";
import React, { useState, useEffect } from "react";
import axios from "axios";

import Sidebar from "../../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import config from "../../../../../config.json";
import { useParams } from "next/navigation";
import { PencilSquareIcon, CheckIcon } from "@heroicons/react/24/outline";
import CommentModal from "@/app/components/CommentModal";
import Loader from "@/app/components/Loader";
import Cookies from "js-cookie";
import UploadSheetModal from "@/app/components/UploadSheetModal";
import { downloadBase64File } from "@/app/helpers/fileDownload";
import { validatePincode } from "@/app/utils/pincodeService";
import { toast, ToastContainer } from "react-toastify";
import Link from "next/link";
import { IoMdArrowBack } from "react-icons/io";

const queryClient = new QueryClient();

export default function Customers({ params }) {
  const [showDetails, setShowDetails] = useState(true);
  const [customerData, setCustomerData]: any = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [editableFields, setEditableFields] = useState({});
  const [updatedData, setUpdatedData]: any = useState({});
  const [customerDbId, setCustomerDbId]: any = useState("");
  const [dataLoading, setDataLoading] = useState(true); // For initial data loading - start as true
  const [editLoading, setEditLoading] = useState(false); // For edit operations
  const [loading, setLoading] = useState(false);
  const [uploadLoading, setUploadLoading] = useState(false); // For document uploads
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [showAddressNote, setShowAddressNote] = useState(false); // For showing address verification note
  const { id } = useParams();
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType]: any = useState("");
  const [comment, setComment]: any = useState("");
  const [customerNumber, setCustomerNumber]: any = useState("");
  const [salespeople, setSalespeople]: any = useState([]);
  const [selectedSalesperson, setSelectedSalesperson]: any = useState(null);

  const [selectedSalespersonInfo, setSelectedSalespersonInfo]: any = useState({
    name: "",
    email: "",
  });

  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [companyExist, setCompanyExist] = useState(true);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const [draftOrderId, setDraftOrderId] = useState("");
  const [modalLoading, setModalLoading] = useState(false);
  const [uploadError, setUploadError] = useState("");
  const [error, setError]: any = useState("");
  const [userRole, setUserRole] = useState(null);
  const [customerNumberError, setCustomerNumberError] = useState("");

  useEffect(() => {
    const role = Cookies.get("userrole");
    setUserRole(role || null);
  }, []);
  // Helper function to determine the display status
  const getDisplayStatus = () => {
    const brn = customerData?.companyDetails?.brn || "";
    const isNABrn = brn.toLowerCase() === "n/a" || brn === "";

    if (customerData?.status === "New Customer" && isNABrn) {
      return "Application in progress";
    }

    return customerData?.status || "New Customer";
  };
  // Define fieldsConfig with customer number editable only when status is "Approved: Level 1"
  const getFieldsConfig = () => [
    { label: "Individual Name", field: "name" },
    { label: "Company Name", field: "companyName", editable: false },
    { label: "Email Address", field: "email", editable: false },
    {
      label: "ECPAC Number",
      field: "customerNumber",
      editable: customerData?.status === "Approved: Level 1",
    },
    { label: "Business Address", field: "companyDetails.businessAddress" },
    { label: "Pincode", field: "companyDetails.pincode" },
    { label: "City", field: "companyDetails.city", editable: false },
    { label: "State", field: "companyDetails.state", editable: false },
    { label: "Country Code", field: "companyDetails.country", editable: false },
    { label: "Business msicCodes", field: "companyDetails.msicCodes" },
    { label: "Business Contact Number", field: "phone" },
    { label: "TIN", field: "companyDetails.tin" },
    { label: "Business Registration No.", field: "companyDetails.brn" },
    { label: "Old Business Registration No.", field: "companyDetails.obrn" },
  ];

  // This will be recalculated whenever this component re-renders
  // and customerData changes, making the customer number editable
  // only when status is "Approved: Level 1"
  const fieldsConfig = getFieldsConfig();

  const [showCustomerInputModal, setShowCustomerInputModal] = useState(true);

  const myHeaders = {
    Authorization: `Bearer ${Cookies.get("token")}`,
    "Content-Type": "application/json",
  };
  const fetchCustomerData = async () => {
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/distributor?shopifyCustomerId=${id}`,
        { headers: myHeaders }
      );
      setCustomerData(response.data.data.data[0]);
      setUpdatedData(response.data.data.data[0]);
      setSelectedSalesperson(response.data.data.data[0]?.salespersonId);
      setCustomerDbId(response.data.data.data[0]?._id);
    } catch (error) {
      console.error("Error fetching customer data:", error);
      setErrorMessage("Failed to fetch customer data. Please try again later.");
      toast.error("Failed to fetch customer data. Please try again later.");
    }
  };

  const handleFileUpload = async (file: File) => {
    setModalLoading(true);
    setUploadError(""); // Clear any previous errors
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("customerId", `${id}`);

      const response = await axios.post(
        `${config.BACKEND_URL}/order/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.status === 207) {
        // Handle error file download
        setIsModalOpen(false);
        setSuccessMessage(
          `Upload completed. ${response.data.data.successCount} records processed successfully. ${response.data.data.errorCount} records had errors. Error file downloaded.`
        );
        toast.success("File uploaded successfully");
        const gid = response.data?.data?.order?.id;
        const id = gid.split("/").pop();
        setDraftOrderId(id);
        setIsConfirmModalOpen(true);
        downloadBase64File(response.data.data.errorFile, "error_records.xlsx");
        return;
      }

      if (response.data.status === "success") {
        setIsModalOpen(false);
        setSuccessMessage("File uploaded successfully");
        toast.success("File uploaded successfully");
        const gid = response.data?.data?.order?.id;
        const id = gid.split("/").pop();
        setDraftOrderId(id);
        setIsConfirmModalOpen(true);
      } else {
        const errorMessage =
          response.data.errors?.[0]?.message || "Upload failed";
        setUploadError(errorMessage);
        toast.error("Upload Failed");
        setErrorMessage(errorMessage);
      }
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.errors?.[0]?.message ||
        "Failed to upload file. Please try again.";
      setUploadError(errorMessage);
      console.error("Upload failed:", error);
      toast.error("Upload Failed");
      setErrorMessage(errorMessage);
    } finally {
      setModalLoading(false);
    }
  };

  const handleCloseModal = async () => {
    try {
      setIsConfirmModalOpen(false);
      const response = await axios.delete(
        `${config.BACKEND_URL}/order/${draftOrderId}`,

        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (response.data.status === "success") {
        setSuccessMessage("Order cancelled successfully");
        toast.success("Upload Failed");
      } else {
        toast.error("Upload Failed");
        setErrorMessage(response.data.errors?.[0]?.message || "Upload failed");
      }
    } catch (error: any) {
      console.log(error);
      setErrorMessage(
        error?.response?.data?.errors?.[0]?.message ||
          "Failed to upload file. Please try again."
      );
      toast.error("Failed to upload file. Please try again.");
    }
  };

  const handleConfirmModal = async () => {
    setConfirmLoading(true);
    try {
      setIsConfirmModalOpen(false);
      const response = await axios.patch(
        `${config.BACKEND_URL}/order/${draftOrderId}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${Cookies.get("token")}`,
            "Content-Type": "application/json",
          },
        }
      );
      if (response.data.status === "success") {
        setSuccessMessage("Order created successfully");
        toast.success("Order created successfully");
      } else {
        setErrorMessage(response.data.errors?.[0]?.message || "Upload failed");
      }

      console.log(response);
    } catch (error) {
      console.log(error);
    } finally {
      setConfirmLoading(false);
    }
  };

  useEffect(() => {
    const loadInitialData = async () => {
      setDataLoading(true);
      try {
        await Promise.all([
          fetchCustomerData(),
          fetchSalespeople(),
          fetchCompany(),
        ]);
      } catch (error) {
        console.error("Error loading initial data:", error);
      } finally {
        setDataLoading(false);
      }
    };

    loadInitialData();
  }, [id]);

  useEffect(() => {
    if (selectedSalesperson && salespeople.length > 0) {
      const selectedPerson = salespeople.find(
        (person) => person._id === selectedSalesperson
      );
      if (selectedPerson) {
        setSelectedSalespersonInfo({
          name: selectedPerson.name,
          email: selectedPerson.email,
        });
      }
    }
  }, [salespeople, selectedSalesperson]);
  const handleInputChange = (field, value) => {
    const fieldParts = field.split(".");

    if (fieldParts.length === 1) {
      setUpdatedData((prev) => ({ ...prev, [field]: value }));
    } else {
      setUpdatedData((prev) => {
        const updated = { ...prev };
        let temp = updated;
        for (let i = 0; i < fieldParts.length - 1; i++) {
          temp = temp[fieldParts[i]];
        }
        temp[fieldParts[fieldParts.length - 1]] = value;
        return updated;
      });
    }
  };

  const toggleEditableField = (field) => {
    setEditableFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };
  const handleSave = async () => {
    if (!updatedData || !customerData) return;
    setShowAddressNote(true);

    if (
      customerData?.status === "Approved: Level 1" &&
      editableFields["customerNumber"] &&
      (!updatedData.customerNumber || !updatedData.customerNumber.trim())
    ) {
      setErrorMessage("Customer number cannot be blank");
      toast.error("Customer number cannot be blank");
      setShowAddressNote(false);
      return;
    }

    if (updatedData.customerNumber) {
      updatedData.customerNumber = updatedData.customerNumber.trim();
    }

    const { email: updatedEmail, ...filteredUpdatedData } = updatedData;
    const { email: originalEmail, ...filteredCustomerData } = customerData;

    setEditLoading(true);
    setErrorMessage("");
    setSuccessMessage("");

    try {
      const response = await axios.patch(
        `${config.BACKEND_URL}/distributor/${customerDbId}`,
        {
          ...filteredUpdatedData,
          salespersonId: selectedSalesperson,
          salespersonInfo: selectedSalespersonInfo,
        },
        { headers: myHeaders }
      );

      fetchCustomerData();
      toast.success("Data successfully updated");
      setSuccessMessage("Data successfully updated");
      setEditMode(false);
      setEditableFields({});
    } catch (error: any) {
      console.error("Error updating customer data:", error);
      if (error?.response?.data?.errors?.length > 0) {
        setErrorMessage(error.response.data.errors[0].message);
      } else {
        setErrorMessage("Error updating customer data.");
      }
      toast.error("Error updating customer data.");
    } finally {
      setEditLoading(false);
      setShowAddressNote(false);
    }
  };

  const fetchSalespeople = async () => {
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/department_people`
      );
      const allPeople = response.data.data.data;
      const salespeople = allPeople.filter((person) =>
        person.departmentType.some(
          (department) => department.department === "Sales Person"
        )
      );
      setSalespeople(salespeople);
    } catch (error) {
      console.error("Error fetching salespeople:", error);
      setErrorMessage(
        "Failed to fetch salespeople data. Please try again later."
      );
      toast.error("Failed to fetch salespeople data. Please try again later.");
    }
  };

  const fetchCompany = async () => {
    try {
      const payload: any = { customerId: params.id };
      const response = await axios.post(
        `${config.BACKEND_URL}/distributor/checkCompany`,
        payload
      );

      const { companyExists } = response.data.data;
      setCompanyExist(companyExists);
    } catch (error: any) {
      console.error("Error fetching company data:", error);
      setErrorMessage(error.response.data.errors[0].message);
      toast.error("Error fetching company data:");
    }
  };

  const openModal = async (type) => {
    setModalType(type);

    if (type === "Approve" && customerData?.companyDetails?.country === "MY") {
      const validation = await validatePincode(
        customerData.companyDetails.pincode,
        customerData.companyDetails.state,
        customerData.companyDetails.country
      );

      if (!validation.isValid) {
        const errorMsg = validation.state
          ? `Postal code ${customerData.companyDetails.pincode} belongs to ${validation.state}, not ${customerData.companyDetails.state}`
          : "Invalid postal code for the selected state/country";

        toast.error(errorMsg);
        return;
      }
    }
    if (type === "Approve" && customerData?.status === "New Customer") {
      await fetchSalespeople();
    }
    setShowModal(true);
  };

  const handleConfirm = async () => {
    if (!comment.trim()) {
      setErrorMessage("Please add comment and the try again!");
      toast.error("Please add comment and the try again!");
      return;
    }

    if (modalType === "Approve" && customerData?.status === "New Customer") {
      if (!customerData?.salespersonId && !selectedSalesperson) {
        setErrorMessage("Please select salesperson and the try again!");
        toast.error("Please select salesperson and the try again!");
        return;
      }
    }
    if (modalType === "Approve" && customerData.status === "New Customer") {
      if (!customerNumber) {
        setErrorMessage("PLease add customer number!");
        toast.error("PLease add customer number!");
        return;
      }
      if (customerNumber) {
        let customerNumberTrimmed = customerNumber.trim();

        // Check if leading/trailing spaces exist OR if there are spaces within the string
        if (
          customerNumberTrimmed !== customerNumber ||
          customerNumber.includes(" ")
        ) {
          setErrorMessage("Customer number field cannot contain spaces");
          toast.error("Customer number field cannot contain spaces");
          return;
        }
      }
    }

    setConfirmLoading(true);
    try {
      const loggedInUser = Cookies.get("userID");
      let endpoint;
      const { email, name } = selectedSalespersonInfo || {
        email: "",
        name: "",
      };
      const payload: any = {
        customerId: customerDbId,
        loggedInUser,
        comment,
        email,
        name,
      };

      if (modalType === "Approve" && customerData?.status === "New Customer") {
        payload.salespersonId = selectedSalesperson;
        payload.salespersonInfo = selectedSalespersonInfo;
      }

      if (modalType === "Approve" && customerData.status === "New Customer") {
        payload.customerNumber = customerNumber;
      }

      switch (modalType) {
        case "Approve":
          endpoint = `${config.BACKEND_URL}/distributor/changeCustomerStatus`;
          break;
        case "Reject":
        case "Ban":
          endpoint = `${config.BACKEND_URL}/distributor/banCustomer`;
          break;
        default:
          return;
      }

      const response = await axios.post(endpoint, payload, {
        headers: myHeaders,
      });
      toast.success(response.data.data.message);
      setSuccessMessage(response.data.data.message);
      // console.log(
      //   "Customer status updated successfully:",
      //   response.data.data.message
      // );
      fetchCustomerData();

      // Close modal and reset form after successful operation
      setShowModal(false);
      setComment(""); // ✅ Reset
      setCustomerNumber(""); // Reset customer number
    } catch (error: any) {
      console.error(
        "Error updating customer status:",
        error.response.data.errors[0].message
      );
      setErrorMessage(error.response.data.errors[0].message);
      toast.error(error.response.data.errors[0].message);
    } finally {
      setConfirmLoading(false);
    }
  };
  const handleDocumentUpload = async (docName, file) => {
    setUploadLoading(true);
    console.log("---customerCom", customerData);
    const updatedDocuments = { ...customerData.companyDetails.documents };
    if (!file) {
      return;
    }

    const formData = new FormData();
    formData.append(docName, file);
    // Object.entries(customerData.companyDetails.documents).forEach(
    //   ([key, value]: any) => {
    //     if (key !== docName) {
    //       formData.append(key, value); // Append existing documents
    //     }
    //   }
    // );
    formData.append("currentStep", "3");
    formData.append(
      "details",
      JSON.stringify({
        entityType: "Limited Liability Company",
      })
    );
    // Prepare existing documents along with the updated one
    // const updatedDocuments = {
    //   ...customerData.companyDetails.documents,
    //   [docName]: URL.createObjectURL(file), // Temporarily use the updated file's URL
    // };

    // Add the details, including entity type and updated documents
    // formData.append(
    //   "details",
    //   JSON.stringify({
    //       entityType: "Limited Liability Company",
    //       documents: updatedDocuments,
    //   })
    // );
    try {
      const response = await fetch(
        `${config.BACKEND_URL}/distributor/${customerData.shopifyCustomerId}`,
        {
          method: "POST",
          body: formData,
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to upload document: ${response.statusText}`);
      }

      const responseData = await response.json();

      fetchCustomerData();
      toast.success("Document uploaded successfully");
      setSuccessMessage("Document uploaded successfully");
      setEditMode(false);
      setEditableFields({});
    } catch (error) {
      console.error("Error uploading document:", error);
    } finally {
      setUploadLoading(false);
    }
  };
  // if (loading) {
  //   return (
  //     <div className="w-full flex justify-center items-center z-10">
  //       <Loader />
  //     </div>
  //   );
  // }
  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="p-8">
          <div className="w-full p-4 flex justify-between items-center rounded-t-lg">
            {dataLoading || !customerData ? (
              <div className="w-full flex justify-center items-center z-10">
                <Loader />
              </div>
            ) : (
              <>
                <div className="flex">
                  <div className="flex gap-3 items-center">
                    <Link href="/customers">
                      <IoMdArrowBack size={30} fill="black" />
                    </Link>
                    <h2 className="text-2xl text-black font-bold">
                      Customer: {params.id}
                    </h2>
                  </div>
                  <span
                    className={`px-2 py-1 rounded text-sm font-semibold mx-4 ${
                      getDisplayStatus() === "New Customer"
                        ? "bg-blue-100 text-blue-800"
                        : getDisplayStatus() === "Approved: Level 1"
                        ? "bg-green-100 text-green-800"
                        : getDisplayStatus() ===
                          "Pending Salesperson Allocation"
                        ? "bg-yellow-100 text-yellow-800"
                        : getDisplayStatus() === "Customer Created in Sage"
                        ? "bg-purple-100 text-purple-800"
                        : getDisplayStatus() === "Inactive"
                        ? "bg-red-100 text-red-800"
                        : getDisplayStatus() === "Rejected"
                        ? "bg-red-100 text-red-800"
                        : getDisplayStatus() === "Application in progress"
                        ? "bg-orange-100 text-orange-800"
                        : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {getDisplayStatus()}
                  </span>
                  <div>
                    {!companyExist && (
                      <span className="px-2 py-1 rounded text-sm font-semibold mx-4 bg-red-100 text-red-800">
                        {" "}
                        No company for this customer
                      </span>
                    )}
                  </div>
                </div>

                {companyExist && (
                  <div className="space-x-3 flex">
                    {showDetails && (
                      <>
                        {getDisplayStatus() === "Customer Created in Sage" &&
                          (userRole === "Sunrise Admin" ||
                            userRole === "Sales Person") && (
                            <button
                              onClick={() => setIsModalOpen(true)}
                              type="button"
                              className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                            >
                              Place Order
                            </button>
                          )}

                        <button
                          onClick={() => {
                            if (editMode) {
                              setEditableFields({});
                            } else {
                              // Set selectedSalesperson to the current value when entering edit mode
                              setSelectedSalesperson(
                                customerData?.salespersonId?._id
                              );
                            }
                            setEditMode(!editMode);
                          }}
                          className=" flex items-center space-x-2 rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                        >
                          <span>
                            {editMode ? "Cancel Edit" : "Edit Details"}
                          </span>
                        </button>
                      </>
                    )}

                    {(() => {
                      const displayStatus = getDisplayStatus();

                      return displayStatus !== "Rejected" &&
                        displayStatus !== "Inactive" &&
                        displayStatus !== "Approved: Level 1" &&
                        displayStatus !== "Application in progress" &&
                        displayStatus !== "Customer Created in Sage" ? (
                        <button
                          onClick={async () => {
                            setLoading(true);
                            try {
                              await openModal("Approve");
                            } finally {
                              setLoading(false);
                            }
                          }}
                          disabled={loading}
                          className={`rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${
                            loading ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          {loading ? "Validating..." : "Approve Customer"}
                        </button>
                      ) : null;
                    })()}
                    {customerData?.status === "Approved: Level 1" &&
                      (userRole === "Sunrise Admin" ||
                        userRole === "Finance Manager" ||
                        userRole === "Finance Executive") && (
                        <button
                          onClick={() => openModal("Approve")}
                          className="rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                        >
                          Create customer in SAGE
                        </button>
                      )}
                    {(() => {
                      const displayStatus = getDisplayStatus();

                      return displayStatus === "New Customer" ||
                        displayStatus === "Application in progress" ||
                        displayStatus === undefined ? (
                        <button
                          onClick={() => openModal("Reject")}
                          className="rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                        >
                          Reject Customer
                        </button>
                      ) : null;
                    })()}

                    {customerData?.status === "Rejected" && (
                      <button
                        onClick={() => openModal("Approve")}
                        className="rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                      >
                        Re-initiate application
                      </button>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
          {!dataLoading && customerData && (
            <>
              <div className="border rounded-b-lg shadow-md my-2">
                <div className="flex border-b">
                  <button
                    className={`w-1/2 p-4 text-center font-semibold border-r rounded-md ${
                      showDetails ? "text-white bg-[#222222]" : "text-black"
                    }`}
                    onClick={() => setShowDetails(true)}
                  >
                    Details
                  </button>
                  <button
                    className={`w-1/2 p-4 text-center font-semibold border-r rounded-md ${
                      !showDetails ? "text-white bg-[#222222]" : "text-black"
                    }`}
                    onClick={() => setShowDetails(false)}
                  >
                    Timeline
                  </button>
                </div>
              </div>

              <div className="my-4">
                {showDetails ? (
                  <div className="relative">
                    {/* Address Verification Note */}
                    {showAddressNote && (
                      <div className="mb-4 p-4 bg-blue-50 border-l-4 border-blue-400 rounded-md">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <svg
                              className="h-5 w-5 text-blue-400"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <p className="text-sm text-blue-700 font-medium">
                              <strong>Important:</strong> Please ensure that the
                              address and postal code information is accurate
                              and complete before saving the changes.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    <div
                      className="overflow-y-auto"
                      style={{ maxHeight: "600px" }}
                    >
                      <table className="w-full text-center bg-white shadow-lg rounded-lg overflow-hidden">
                        <thead>
                          <tr className="bg-[#222222]">
                            <th className="w-1/2 px-4 text-white py-2 border-r border-gray-400">
                              Detail
                            </th>
                            <th className="w-1/2 px-4 text-white py-2">
                              Value
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {fieldsConfig.map(
                            ({ label, field, editable = true }) => {
                              const fieldParts = field.split(".");
                              const value = fieldParts.reduce(
                                (acc, curr) => acc?.[curr],
                                updatedData
                              );

                              // Only customize ECPAC Number field
                              if (field === "customerNumber") {
                                return (
                                  <tr className="border-b" key={field}>
                                    <td className="py-3 px-4 text-black font-bold text-center">
                                      {label}
                                    </td>
                                    <td className="py-3 px-4 text-black flex justify-between items-center">
                                      <span className="text-center flex-grow">
                                        {editableFields[field] ? (
                                          editable ? (
                                            <>
                                              <input
                                                type="text"
                                                value={value || ""}
                                                onChange={(e) => {
                                                  const inputValue =
                                                    e.target.value;
                                                  const upperValue =
                                                    inputValue.toUpperCase();
                                                  const cleanValue =
                                                    upperValue.replace(
                                                      /[^A-Z0-9]/g,
                                                      ""
                                                    );
                                                  const truncatedValue =
                                                    cleanValue.slice(0, 8);
                                                  handleInputChange(
                                                    field,
                                                    truncatedValue
                                                  );
                                                  if (
                                                    truncatedValue.length === 8
                                                  ) {
                                                    setCustomerNumberError("");
                                                  } else if (
                                                    truncatedValue.length > 0
                                                  ) {
                                                    setCustomerNumberError(
                                                      "Customer Number must be exactly 8 characters"
                                                    );
                                                  } else {
                                                    setCustomerNumberError("");
                                                  }
                                                }}
                                                className="border p-2 rounded w-full text-center"
                                                maxLength={8}
                                              />
                                              {customerNumberError && (
                                                <p className="text-red-500 text-sm mt-1">
                                                  {customerNumberError}
                                                </p>
                                              )}
                                            </>
                                          ) : (
                                            value || "N/A"
                                          )
                                        ) : (
                                          value || "N/A"
                                        )}
                                      </span>
                                      {editMode && editable && (
                                        <PencilSquareIcon
                                          className="h-5 w-5 text-gray-600 cursor-pointer ml-4"
                                          onClick={() =>
                                            toggleEditableField(field)
                                          }
                                        />
                                      )}
                                    </td>
                                  </tr>
                                );
                              }
                              return (
                                <tr className="border-b" key={field}>
                                  <td className="py-3 px-4 text-black font-bold text-center">
                                    {label}
                                  </td>
                                  <td className="py-3 px-4 text-black flex justify-between items-center">
                                    <span className="text-center flex-grow">
                                      {editableFields[field] ? (
                                        editable ? (
                                          <input
                                            type="text"
                                            value={value || ""}
                                            onChange={(e) =>
                                              handleInputChange(
                                                field,
                                                e.target.value
                                              )
                                            }
                                            className="border p-2 rounded w-full text-center"
                                          />
                                        ) : (
                                          value || "N/A"
                                        )
                                      ) : (
                                        value || "N/A"
                                      )}
                                    </span>
                                    {editMode && editable && (
                                      <PencilSquareIcon
                                        className="h-5 w-5 text-gray-600 cursor-pointer ml-4"
                                        onClick={() =>
                                          toggleEditableField(field)
                                        }
                                      />
                                    )}
                                  </td>
                                </tr>
                              );
                            }
                          )}

                          <tr className="border-b">
                            <td className="py-3 px-4 text-black font-bold text-center">
                              Salesperson*
                            </td>
                            {!editMode ? (
                              <td className="py-3 px-4 text-black text-center">
                                {customerData?.salespersonName || "N/A"}
                              </td>
                            ) : (
                              // <td className="py-3 px-4 text-black text-center">
                              //   <div className="my-4 flex flex-row">
                              //     <select
                              //       value={selectedSalesperson}
                              //       onChange={(e) =>
                              //         setSelectedSalesperson(e.target.value)
                              //       }
                              //       className="border rounded w-full text-gray-700"
                              //     >
                              //       <option value="">
                              //         Select a salesperson
                              //       </option>
                              //       {salespeople.map((person) => (
                              //         <option
                              //           key={person._id}
                              //           value={person._id}
                              //         >
                              //           {person.name} ({person.email})
                              //         </option>
                              //       ))}
                              //     </select>
                              //     <span>
                              //       {editMode && (
                              //         <PencilSquareIcon
                              //           className="h-5 w-5 text-gray-600 cursor-pointer ml-4"
                              //           // onClick={() => toggleEditableField(field)}
                              //         />
                              //       )}
                              //     </span>
                              //   </div>
                              // </td>
                              <td className="py-3 px-4 text-black text-center">
                                <div className="my-4 flex flex-row">
                                  {customerData.status !== "New Customer" ? (
                                    <select
                                      value={selectedSalesperson}
                                      onChange={(e) => {
                                        const selectedId = e.target.value;
                                        setSelectedSalesperson(selectedId);

                                        // Find the selected person to get their name and email
                                        const selectedPerson = salespeople.find(
                                          (person) => person._id === selectedId
                                        );
                                        if (selectedPerson) {
                                          setSelectedSalespersonInfo({
                                            name: selectedPerson.name,
                                            email: selectedPerson.email,
                                          });
                                        }
                                      }}
                                      disabled={
                                        !(
                                          userRole === "Sunrise Admin" ||
                                          userRole === "Finance Manager" ||
                                          userRole === "Finance Executive"
                                        )
                                      }
                                      className={`border rounded w-full text-gray-700 ${
                                        !(
                                          userRole === "Sunrise Admin" ||
                                          userRole === "Finance Manager" ||
                                          userRole === "Finance Executive"
                                        )
                                          ? "bg-gray-100 cursor-not-allowed opacity-60"
                                          : ""
                                      }`}
                                    >
                                      <option value="">
                                        Select a salesperson
                                      </option>
                                      {salespeople.map((person) => (
                                        <option
                                          key={person._id}
                                          value={person._id}
                                        >
                                          {person.name} ({person.email})
                                        </option>
                                      ))}
                                    </select>
                                  ) : (
                                    <span className=" text-black text-center">
                                      {`Editing not allowed for "New Customer"`}
                                    </span>
                                  )}
                                  <span>
                                    {editMode &&
                                      customerData.status !==
                                        "New Customer" && (
                                        <PencilSquareIcon className="h-5 w-5 text-gray-600 cursor-pointer ml-4" />
                                      )}
                                  </span>
                                </div>
                              </td>
                            )}
                          </tr>
                          {/* )} */}
                          {/* New rows for displaying documents */}
                          {customerData?.companyDetails?.documents &&
                            Object.entries(
                              customerData.companyDetails.documents
                            ).map(([docName, docUrl]) => (
                              <tr className="border-b" key={docName}>
                                <td className="py-3 px-4 text-black font-bold text-center">
                                  {docName.charAt(0).toUpperCase() +
                                    docName.slice(1)}
                                </td>
                                <td className="py-3 px-4 text-black flex justify-between items-center">
                                  <span className="flex-grow text-center">
                                    {editMode ? (
                                      <>
                                        <div className="flex flex-row items-center justify-center">
                                          <span className="mr-2">
                                            <a
                                              href={docUrl as string}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="text-blue-500"
                                            >
                                              {typeof docUrl === "string"
                                                ? docUrl.split("/").pop()
                                                : ""}
                                            </a>
                                          </span>
                                          <input
                                            type="file"
                                            onChange={(e) => {
                                              if (
                                                e.target.files &&
                                                e.target.files.length > 0
                                              ) {
                                                handleDocumentUpload(
                                                  docName,
                                                  e.target.files[0]
                                                );
                                              }
                                            }}
                                          />
                                        </div>
                                      </>
                                    ) : (
                                      <a
                                        href={docUrl as string}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500"
                                      >
                                        {typeof docUrl === "string"
                                          ? docUrl.split("/").pop()
                                          : ""}
                                      </a>
                                    )}
                                  </span>
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  // Timeline section remains unchanged
                  <div className="bg-white shadow-lg rounded-lg overflow-hidden text-center">
                    <div
                      className="overflow-y-auto"
                      style={{ maxHeight: "600px" }}
                    >
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-[#222222]">
                            <th className="w-1/2 px-4 text-white py-2 border-r border-gray-400">
                              Date
                            </th>
                            <th className="w-1/2 px-4 text-white py-2">
                              Comment
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {customerData.timeline.map((entry, index) => (
                            <tr
                              key={index}
                              className={
                                index % 2 === 0 ? "bg-gray-100" : "bg-white"
                              }
                            >
                              <td className="w-1/2 py-3 px-4 text-black  justify-between items-center">
                                {new Date(entry.date).toLocaleDateString()}
                              </td>
                              <td className="w-1/2 px-4 text-black py-2">
                                {entry.comment}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
              {showDetails && (
                <>
                  {editMode && (
                    <div className="flex justify-end mt-4">
                      <button
                        onClick={handleSave}
                        disabled={editLoading}
                        className={`bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 flex items-center justify-center w-[85px] ${
                          editLoading ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                      >
                        {editLoading ? (
                          <svg
                            className="animate-spin h-5 w-5 text-white  "
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            />
                            <path
                              className="opacity-75"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="4"
                              strokeLinecap="round"
                              d="M22 12a10 10 0 00-10-10"
                            />
                          </svg>
                        ) : (
                          "Save"
                        )}
                      </button>
                    </div>
                  )}
                </>
              )}

              {showModal && (
                <CommentModal
                  title={`Confirm ${modalType}`}
                  message={""}
                  onClose={() => setShowModal(false)}
                  onConfirm={handleConfirm}
                  confirmLoading={confirmLoading}
                >
                  {modalType === "Approve" &&
                    customerData?.status === "New Customer" && (
                      <div className="my-4">
                        <label className="block text-black mb-2 font-bold">
                          Select Salesperson:
                        </label>
                        <select
                          value={selectedSalesperson}
                          onChange={(e) => {
                            const selectedId = e.target.value;
                            setSelectedSalesperson(selectedId);

                            // Find the selected person to get their name and email
                            const selectedPerson = salespeople.find(
                              (person) => person._id === selectedId
                            );
                            if (selectedPerson) {
                              setSelectedSalespersonInfo({
                                name: selectedPerson.name,
                                email: selectedPerson.email,
                              });
                            }
                          }}
                          disabled={
                            !(
                              userRole === "Sunrise Admin" ||
                              userRole === "Finance Manager" ||
                              userRole === "Finance Executive"
                            )
                          }
                          className={`border rounded w-full text-gray-700 ${
                            !(
                              userRole === "Sunrise Admin" ||
                              userRole === "Finance Manager" ||
                              userRole === "Finance Executive"
                            )
                              ? "bg-gray-100 cursor-not-allowed opacity-60"
                              : ""
                          }`}
                        >
                          <option value="">Select a salesperson</option>
                          {salespeople.map((person) => (
                            <option key={person._id} value={person._id}>
                              {person.name} ({person.email})
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                  {modalType === "Approve" &&
                    customerData?.status === "New Customer" && (
                      <div className="my-4">
                        <label className="block text-black mb-2 font-bold">
                          ECPAC Number
                        </label>
                        <textarea
                          value={customerNumber}
                          onChange={(e) => {
                            const value = e.target.value;
                            const upperValue = value.toUpperCase();

                            // Only allow letters and numbers, no special characters
                            const cleanValue = upperValue.replace(
                              /[^A-Z0-9]/g,
                              ""
                            );

                            // Limit to 8 characters
                            const truncatedValue = cleanValue.slice(0, 8);

                            setCustomerNumber(truncatedValue);

                            if (truncatedValue.length === 8) {
                              setCustomerNumberError("");
                            } else if (truncatedValue.length > 0) {
                              setCustomerNumberError(
                                "Customer Number must be exactly 8 characters"
                              );
                            } else {
                              setCustomerNumberError("");
                            }
                          }}
                          className="p-2 border rounded text-black w-full h-10 overflow-hidden"
                          placeholder="Add customer number"
                          maxLength={8}
                          required
                        />
                        {customerNumberError && (
                          <p className="text-red-500 text-sm mt-1">
                            {customerNumberError}
                          </p>
                        )}
                      </div>
                    )}
                  <label className="block text-black mb-2 font-bold">
                    Add Comment
                  </label>
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="p-2 border rounded text-black w-full  overflow-hidden"
                    placeholder="Add your comment"
                    required
                  />
                </CommentModal>
              )}
              <UploadSheetModal
                isOpen={isModalOpen}
                onClose={() => {
                  setIsModalOpen(false);
                  setUploadError(""); // Clear error when modal is closed
                }}
                onSubmit={handleFileUpload}
                sampleFileUrl={`${config.BACKEND_URL}/order/sample-sheet`}
                title="Upload Sheet"
                acceptedFileTypes=".xlsx,.xls,.csv"
                fullStockSheetUrl={`${config.BACKEND_URL}/order/full-stock-sheet?customerId=${id}`}
                loading={modalLoading}
                error={uploadError}
              />
              {isConfirmModalOpen && (
                <CommentModal
                  onClose={handleCloseModal}
                  title="Do you want to place order ?"
                  message={successMessage || "File uploaded successfully"}
                  onConfirm={handleConfirmModal}
                  confirmLoading={confirmLoading}
                />
              )}
            </>
          )}
        </div>
      </Sidebar>
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </QueryClientProvider>
  );
}
