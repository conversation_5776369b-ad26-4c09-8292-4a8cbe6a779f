import React from "react";

const SuccessModal = ({ successMessage, onClose }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-96">
        <h2 className="text-xl font-semibold text-green-600 mb-4">
          Success
        </h2>
        <p className="text-black mb-4">{successMessage}</p>
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
