import CardTable from "./cardTable";

export default function Card({title, description, buttonText, buttonLink}) {
    return (
      <div className="bg-white px-2 py-3 sm:px-4">
        <div className="-ml-4 -mt-4 flex flex-wrap items-center justify-between sm:flex-nowrap">
          <div className="ml-4 mt-4">
            <div className="flex justify-between">
                <h3 className="text-base font-semibold leading-6 text-gray-900">{title}</h3>
                <button
                type="button"
                className="relative inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                {buttonText}
                </button>
            </div>
            {/* <p className="mt-1 text-sm text-[#222222]"> */}
              <CardTable />
            {/* </p> */}
          </div>
          <div className="ml-4 mt-4 flex-shrink-0">
          </div>
        </div>
      </div>
    )
  }
  