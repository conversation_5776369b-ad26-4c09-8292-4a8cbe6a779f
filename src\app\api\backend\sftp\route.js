import axios from "axios";
import config from "../../../../../config.json";

export async function GET(request) {
  // const response = await axios.post(`${config.BACKEND_URL}/sftp`);
  const response = {
    //commnted for now
    data: {
      sftp: "not linked yet",
    },
  };

  return new Response(JSON.stringify(response.data), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
