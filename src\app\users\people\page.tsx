import config from "../../../../config.json";
import { cookies } from 'next/headers';
import PeoplesPage from './PeoplesPage';

export default async function GetRolePageData() {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  const response = await fetch(`${config.BACKEND_URL}/usergroup`, requestOptions);
  const data = await response.json();

  const userRequestOptions = {
    method: "GET",
  };

  const userPromise = await fetch(`${config.BACKEND_URL}/user`, userRequestOptions);
  const userData = await userPromise.json();
  
  return <PeoplesPage userGroup={data} user={userData} />;
}
