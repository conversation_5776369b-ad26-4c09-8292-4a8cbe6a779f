"use client";

import React, { useCallback, useEffect, useState } from "react";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import Sidebar from "@/app/components/sidebar";
import { PencilIcon, TrashIcon } from "@heroicons/react/20/solid";
import PeopleModal from "@/app/components/peopleModal";
import config from "../../../../config.json";
import { DeleteConfirmationModal } from "@/app/components/deleteConfirmationModal";
import Cookies from "js-cookie";

interface UserGroup {
  _id: string;
  name?: string;
}

interface User {
  _id: any;
  email: string;
  name: string;
  username: string;
  role: string;
  userGroup: any;
}

interface ApiResponse {
  data: {
    data: User[];
  };
}

const queryClient = new QueryClient();

export default function PeoplesPage({ userGroup, user }) {
  console.log(userGroup, " user group");
  console.log(user, " user");

  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState();
  const [addNewUser, setAddNewUser] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  const [userGroupData, setUserGroupData] = useState<UserGroup[]>(
    userGroup.data.data
  );
  const [userData, setUserData] = useState<ApiResponse>(user);

  const fetchUserData = () => {
    const requestOptions = {
      method: "GET",
    };

    fetch(`${config.BACKEND_URL}/user`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        setUserData(result);
      })
      .catch((error) => {
        console.error("Fetching error:", error);
        setUserData({ data: { data: [] } });
      });
  };

  //   const fetchUserGroupData = useCallback(() => {
  //     const requestOptions = {
  //       method: "GET",
  //     };

  //     fetch(
  //       `${config.BACKEND_URL}/usergroup`,
  //       requestOptions
  //     )
  //       .then((response) => response.json())
  //       .then((result) => {
  //         if(result && result.data && Array.isArray(result.data.data)) {
  //           setUserGroupData(result.data.data);
  //         } else {
  //           throw new Error('Invalid user group data');
  //         }
  //       })
  //       .catch((error) => {
  //         console.error("Fetching user group data error:", error);
  //         setUserGroupData([]);
  //       });
  //   }, []);

  //   useEffect(() => {
  //     fetchUserData();
  //     fetchUserGroupData();
  //   }, [fetchUserData, fetchUserGroupData]);

  const getUserGroupName = (userGroup) => {
    if (!userGroup) return "";
    const group = userGroupData.find((g) => g._id == userGroup[0]);
    return group ? group.name : "Group not found";
  };

  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [confirmationOpen, setConfirmationOpen] = useState(false);

  const [newScopes, setNewScopes]: any = useState({});

  useEffect(() => {
    const scopes = Cookies.get("type");
    const getScopes = scopes && JSON.parse(scopes);
    setNewScopes(getScopes);
  }, []);

  function deleteUser(id: any) {
    setCurrentId(id);
    setCurrentType("user");
    setConfirmationOpen(true);
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h1 className="text-2xl font-semibold leading-6 text-gray-900">
                Users
              </h1>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                onClick={() => {
                  setAddNewUser(true);
                  setOpen(true);
                  setEdit(false);
                }}
              >
                Add Users
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6"
                        >
                          S. NO
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                        >
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                        >
                          Email
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                        >
                          Role
                        </th>
                        {(newScopes &&
                          newScopes?.user?.length > 0 &&
                          newScopes?.user?.includes("write")) ||
                        newScopes?.user?.includes("delete") ? (
                          <th
                            scope="col"
                            className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Action
                          </th>
                        ) : (
                          ""
                        )}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {userData?.data?.data.map((user, index) => (
                        <tr key={user._id}>
                          <td className="whitespace-nowrap py-3.5 pl-4 pr-3 text-sm text-[#222222]">
                            {index + 1}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                            {user.name}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                            {user.username}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                            {getUserGroupName(user.userGroup)}
                          </td>
                          {newScopes &&
                            newScopes.user?.length > 0 &&
                            (newScopes.user.includes("write") ||
                              newScopes.user.includes("delete")) && (
                              <td className="relative text-left whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-6">
                                {!isSuperAdmin && (
                                  <>
                                    {newScopes.user.includes("write") && (
                                      <button
                                        type="submit"
                                        className="rounded-full bg-[#222222] p-2 text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                                        onClick={() => {
                                          setOpen(!open);
                                          setEdit(true);
                                          setEditId(user._id);
                                        }}
                                      >
                                        <PencilIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    )}

                                    {newScopes.user.includes("delete") && (
                                      <button
                                        type="button"
                                        className="rounded-full bg-[#222222] p-2 ml-4 text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                                        onClick={() => deleteUser(user._id)}
                                      >
                                        <TrashIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    )}
                                  </>
                                )}
                              </td>
                            )}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <PeopleModal
          open={open}
          setOpen={setOpen}
          fetchData={fetchUserData}
          updatePeople={edit}
          editId={editId}
          addNewPeople={addNewUser}
          userGroupData={userGroupData}
        />
        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchUserData}
            setTableData={setUserData}
          />
        )}
      </Sidebar>
    </QueryClientProvider>
  );
}
