import { useEffect, useState } from "react";
import config from "../../../config.json";

const stats = [
  { name: "NEW", stat: "48.97%" },
  { name: "ACTIVE", stat: "26.46%" },
  { name: "DROP", stat: "24.57%" },
];

export default function ReportFour() {

  const [apiData, setapiData] = useState<any>([]);
  
  useEffect(() => {

      const requestOptions = {
        method: "GET",
      };
    
      fetch(`${config.BACKEND_URL}/pmrStatus`, requestOptions)
        .then(response => {
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          return response.json();
        })
        .then(result => {
          setapiData( result );
        })
        .catch(error => {
          setapiData(null);  
        });
  }, []);

  return (
    <div>
      <h3 className="text-base font-semibold leading-6 text-gray-900">
        Quality of Orders: based on PMR (Last 30 days)
      </h3>
      {apiData && apiData.length > 0 ? (  // data check
        <dl className="mt-5 grid grid-cols-1 gap-5 sm:grid-cols-3">
          {apiData.map((item) => (
            <div
              key={item.pmrStatus}
              className="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
            >
              <dt className="truncate text-sm font-medium text-[#222222]">
                {item.pmrStatus.toUpperCase()}
              </dt>
              <dd className="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
                {`${item.percentage.toFixed(2)} %`}
              </dd>
            </div>
          ))}
        </dl>
      ) : (
        <p className="mt-5 text-center text-[#222222]">
          No data available for PMR status.
        </p>
      )}
    </div>
  );
}
