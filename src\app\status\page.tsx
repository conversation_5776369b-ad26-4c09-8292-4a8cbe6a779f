import config from "../../../config.json";
import { cookies } from 'next/headers';
import StatusPage from "./statusPage";

export default async function Status() {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  console.log(`${config.BACKEND_URL}/status`, " config.BACKEND_URLconfig.BACKEND_URL")

  const responseStatusPromise = await fetch(`${config.BACKEND_URL}/status`, requestOptions);
  const responseStatus = await responseStatusPromise.json();

  let statusData = responseStatus.data.data.map((x: any) => {
    return {
      id: x._id,
      colorCode: x.colorCode,
      departmentType: x.departmentType?.department,
      statusType: x.statusType,
      statusName: x.status,
      initial: x.isInitialStatus,
    };
  });


  const responseDepartmentPromise = await fetch(`${config.BACKEND_URL}/department_type`, requestOptions);
  const responseDepartment = await responseDepartmentPromise.json();
  let departmentData = responseDepartment.data.data.map((x: any) => {
    return {
      optionName: x.department,
      optionKey: x._id,
    };
  });
    
  return <StatusPage statusData={statusData} departmentData={departmentData} />;
}
