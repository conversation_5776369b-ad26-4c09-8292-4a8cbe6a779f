import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../config.json";
import Cookies from "js-cookie";

export function DeleteConfirmationModal({
  id,
  type,
  open,
  setOpen,
  fetchDistributorData,
  setTableData,
  personName = "",
  personEmail = "",
  personDepartment = "",
}) {
  const [error, setError] = useState<string | null>("");
  const [loading, setLoading] = useState(false);

  async function deleteDataFromERP() {
    try {
      const data = await fetchDistributorData();
      const key = data.find((x) => x.id === id);

      if (key.departmentType === "Sales Person" && key.salesPersonKey) {
        const response = await axios.delete(
          `${config.BACKEND_URL}/ERPSync/salesPersons/${key.salesPersonKey}`,
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        );
        return response?.status === 204 ? true : false;
      } else {
        return true;
      }
    } catch (error: any) {
      console.log(error);
      if (error?.response?.status === 409) {
        setError(
          "You can delete a salesperson record only if it is not used by any customers or customer ship-to locations"
        );
      } else if (error?.response?.status === 404) {
        setError(
          "A record cannot be found for the specified entity key in ERP"
        );
      }
    }
  }

  async function deleteConfirmation() {
    setError(null); // Clear previous errors
    setLoading(true);
    try {
      if (type === "department_people") {
        const result = await deleteDataFromERP();
        if (result) {
          await axios.request({
            method: "delete",
            url: `${config.BACKEND_URL}/${type}/${id}`,
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${Cookies.get("token")}`,
            },
          });
          setLoading(false);
          setOpen(false); // Close modal on success
        }
      } else {
        await axios.request({
          method: "delete",
          url: `${config.BACKEND_URL}/${type}/${id}`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        });
        setLoading(false);
        setOpen(false); // Close modal on success
      }

      const data = await fetchDistributorData();

      if (type !== "user") {
        updatePriority(data);
      }
      setLoading(false);
      setTableData(data);
    } catch (err: any) {
      setLoading(false);
      console.log(err, "error");
      setError(
        err.response?.data?.message ||
          "Failed to delete the item. Please try again."
      ); // Set error message
    }
  }

  function updatePriority(changedData) {
    const updatedData = changedData?.map((value, index) => {
      if (value.hasOwnProperty("hierarchy")) {
        value.hierarchy = index;
      }
      if (value.hasOwnProperty("priority")) {
        value.priority = index;
      }
      return value;
    });

    setTableData(updatedData);
  }

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog className="relative z-10" onClose={() => setOpen(false)}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      {type === "department_people" &&
                      personName &&
                      personEmail &&
                      personDepartment ? (
                        <>
                          Are you sure you want to delete{" "}
                          <span className="font-bold">{personName}</span> (
                          <span className="font-mono">
                            {personEmail.length > 20
                              ? `${personEmail.slice(
                                  0,
                                  10
                                )}...${personEmail.slice(-7)}`
                              : personEmail}
                          </span>
                          ) in{" "}
                          <span className="font-bold">{personDepartment}</span>?
                        </>
                      ) : (
                        <>Are You Sure?</>
                      )}
                    </Dialog.Title>
                    <div className="mt-2">
                      {error && <p className="text-sm text-red-500">{error}</p>}
                    </div>
                  </div>
                </div>
                <div className="mt-5 flex sm:mt-6">
                  <button
                    type="button"
                    className={`mr-2 inline-flex w-2/4 justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm ${
                      error ? "opacity-50 cursor-not-allowed" : ""
                    } focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2`}
                    onClick={deleteConfirmation}
                    disabled={!!error || loading}
                  >
                    {loading ? "loading..." : "Confirm"}
                  </button>
                  <button
                    type="button"
                    className="inline-flex w-2/4 justify-center text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md text-sm font-semibold shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                    onClick={() => setOpen(false)}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
