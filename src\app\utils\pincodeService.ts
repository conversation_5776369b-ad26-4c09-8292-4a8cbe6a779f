const MALAYSIAN_STATE_MAPPING: Record<string, string[]> = {
  JHR: ["johor", "johor darul takzim"],
  KDH: ["kedah", "kedah darul aman"],
  KTN: ["kelantan", "kelantan darul naim"],
  MLK: ["melaka", "malacca"],
  NSN: ["negeri sembilan", "negeri sembilan darul khusus"],
  PHG: ["pahang", "pahang darul makmur"],
  PRK: ["perak", "perak darul ridzuan"],
  PLS: ["perlis", "perlis indera kayangan"],
  PNG: ["pulau pinang", "penang"],
  SBH: ["sabah"],
  SWK: ["sarawak"],
  SGR: ["selangor", "selangor darul ehsan"],
  TRG: ["terengganu", "terengganu darul iman"],
  KUL: ["kuala lumpur"],
  LBN: ["labuan"],
  PJY: ["putrajaya"],
};

export const validatePincode = async (
  pincode: string,
  expectedState: string,
  countryCode: string = "MY"
) => {
  try {
    const response = await fetch(
      `https://api.zippopotam.us/${countryCode.toLowerCase()}/${pincode}`
    );

    if (response.status === 404) {
      return { isValid: false, error: "Invalid postal code" };
    }
    

    const data = await response.json();
    const apiState = data.places?.[0]?.state?.toLowerCase();

    if (apiState) {
      const expectedNames = MALAYSIAN_STATE_MAPPING[
        expectedState.toUpperCase()
      ] || [expectedState.toLowerCase()];
      const isValid = expectedNames.some((name) => apiState.includes(name));

      return {
        isValid,
        state: apiState,
        normalizedState: expectedState.toUpperCase(),
      };
    }

    return { isValid: false, error: "State not found for this pincode" };
  } catch (error) {
    console.error("Pincode validation error:", error);
    return { isValid: false, error: "Failed to validate pincode" };
  }
};
