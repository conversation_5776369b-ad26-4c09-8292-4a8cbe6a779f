"use client";
import React, { useEffect, useState, useCallback } from "react";
import Sidebar from "../../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import axios from "axios";
import config from "../../../../../config.json";
import currency from "../../../../../Currency.json";
import ActionModal from "@/app/components/actionModal";
import { formatToAMPM } from "@/app/utils/auth";
import { ArrowPathIcon } from "@heroicons/react/20/solid";
import { usePathname } from "next/navigation";
import Image from "next/image";
import summaryIcon from "../../../assets/summaryIcon.png";
import distributorIcon from "../../../assets/distributorIcon.png";
import Cookies from "js-cookie";
import Link from "next/link";
import { IoMdArrowBack } from "react-icons/io";

const queryClient = new QueryClient();

// interface ActionButton {
//   // actions: any;
//   [key: string]: any;
// }

interface ActionButton {
  actions: Action[];
  [key: string]: any; // Any other properties with any type
}

interface Action {
  [key: string]: any;
}

export default function Orders({ params }) {
  const [detailsTableData, setdetailsTableData] = useState<any[]>([]);
  const [shipmentTableData, setShipmentTableData] = useState<any>([]);
  const [attributeTableData, setattributeTableData] = useState([]);
  const [attributeData, setattributeData] = useState([]);
  const [actionButton, setActionButton] = useState<ActionButton[]>([]);
  const [orderNumber, setOrderNumber] = useState([]);
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [error, setError] = useState<any>();

  let [summaryDataDetails, setSummaryDataDetails]: any = useState({
    id: "",
    deliveryType: "",
    createdAt: "",
    status: "",
    trackingID: "",
    trackingLink: "",
  });

  const [shippingAddress, setShippingAddress] = useState({
    address: "",
    pincode: "",
    country: "",
  });
  const [tabs, setTabs] = useState([
    { name: "Details", current: true },
    { name: "Attributes", current: false },
    { name: "Timeline", current: false },
  ]);
  function changeTab(name: any) {
    setTabs((prevTabs) =>
      prevTabs.map((tab) =>
        tab.name === name
          ? { ...tab, current: true }
          : { ...tab, current: false }
      )
    );
  }
  async function fetchShipmentDataSingle() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/shipment/${params.id}`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("token")}`,
      },
    });
    console.log("-shop re", response.data.shipment);
    setOrderNumber(response.data.shipment.name);
    setActionButton(response.data.actions);
    setShipmentTableData(response.data.shipment);
    setattributeTableData(response.data.shipment.timeline);
    let attributeData = response.data.shipment.attributes.map((x: any) => {
      const emailsByStatus = Array.isArray(x.value)
        ? x.value.reduce((acc, item) => {
            if (item.email) {
              const status = item.status;
              const emails =
                item.email || (item.emailInfo || []).map((info) => info.email);
              if (emails) {
                acc[status] = (acc[status] || []).concat(emails); // Initialize or concatenate emails
              }
              return acc;
            } else {
              return x.value;
            }
          }, {})
        : x.value;
      return {
        name: x.name,
        type: x.type,
        value: JSON.stringify(emailsByStatus),
      };
    });
    setattributeData(attributeData);
    let data = response.data.shipment.lineItems.map((x: any) => {
      return {
        image: x.image,
        sku: x.sku,
        name: x?.variantTitle?.includes("Default Title")
          ? x.productTitle
          : x.variantTitle,
        status: response.data.shipment.status?.status,
        colorCode: response.data.shipment.status?.colorCode,
        qty: x.requested,
        price: x.price,
        metafieldValue: x.metaFieldBudgetCategory,
      };
    });

    setSummaryDataDetails({
      id: response.data.shipment.internalRef,
      deliveryType: "Standard",
      createdAt: new Date(response.data.shipment.order.createdAt)
        .toString()
        .slice(0, 15),
      status: response.data.shipment.status?.status,
      trackingID: "-",
      trackingLink: "-",
    });
    setShippingAddress({
      address: response?.data?.address.businessAddress,
      pincode: response?.data?.address.pincode,
      country: response?.data?.address.country,
    });
    setdetailsTableData(data);
    setRefreshButtonClicked(false);
  }
  console.log("-shipAdd", shippingAddress);
  const [open, setOpen] = useState(false);
  const [formId, setFormId] = useState();

  useEffect(() => {
    fetchShipmentDataSingle();
  }, [tabs, open]);

  useEffect(() => {
    let timeout;
    if (error) {
      timeout = setTimeout(() => {
        setError(null);
      }, 7000);
    }
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [error]);

  function calculateTotalPrice(price, qty) {
    const total = Number(qty) * Number(price);
    return total.toFixed(2);
  }

  const escalationTriggerPeriod = shipmentTableData?.sla?.days;
  const createdAt = new Date(shipmentTableData?.sla?.createdAt);

  const escalationDate = new Date(createdAt);
  escalationDate.setDate(escalationDate.getDate() + escalationTriggerPeriod);

  const today = new Date();

  // const differenceInTime = escalationDate.getTime() - today.getTime();
  // const differenceInDays = Math.ceil(differenceInTime / (1000 * 3600 * 24));

  const differenceInTime = escalationDate.getTime() - today.getTime();
  const differenceInDays =
    Math.ceil(Math.abs(differenceInTime) / (1000 * 3600 * 24)) || 0;

  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }

  const handleLink = (value) => {
    console.log(value, "lonk valu");
  };

  const handleRefreshButtonClicked = () => {
    setRefreshButtonClicked(true);
    fetchShipmentDataSingle();
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="flex p-4 items-center">
          <div className="flex gap-3 items-center">
            <Link href="/shipments">
              <IoMdArrowBack size={30} fill="black" />
            </Link>
            <h2 className="text-3xl font-semibold leading-6 text-gray-900">
              Shipment: {orderNumber}
            </h2>
          </div>

          <button
            type="button"
            className="flex justify-around w-[5%] mr-4 ml-auto rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
            onClick={handleRefreshButtonClicked}
          >
            <ArrowPathIcon
              className={`${
                refreshButtonClicked ? "animate-spin" : "animate-none"
              } h-5 w-5 `}
            />
          </button>
        </div>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="mt-8 flow-root">
            {error && (
              <p className="text-red-600 text-right font-bold">{error}</p>
            )}
            <div className="flex items-baseline justify-between sm:flex-auto lg:block 2xl:flex">
              <div className="flex items-baseline">
                <div className="2xl:ml-4 ">
                  <h2
                    className={`text-base font-semibold leading-6 text-gray-900 px-3  py-4`}
                    style={{
                      backgroundColor: detailsTableData[0]?.colorCode,
                      borderRadius: "0.5rem",
                      marginTop: "1rem",
                    }}
                  >
                    <span className="">{summaryDataDetails.status}</span>
                  </h2>
                </div>
                {differenceInDays > 0 && (
                  <div className="ml-4">
                    <h2
                      className={`text-base font-semibold leading-6 text-gray-900`}
                      style={{
                        backgroundColor: detailsTableData[0]?.colorCode,
                        padding: "0.75rem 2rem",
                        borderRadius: "0.5rem",
                        marginTop: "1rem",
                      }}
                    >
                      <span className="">{differenceInDays} days left.</span>
                    </h2>
                  </div>
                )}
              </div>
              <div className="flex ">
                {actionButton?.map((value, index) => {
                  return (
                    <div key={index} className="my-2 mr-2 2xl:m-2">
                      <button
                        className="rounded-md bg-[#222222] px-3 py-2 text-sm font-medium text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                        type="button"
                        onClick={() => {
                          setFormId(value._id);
                          setOpen(true);
                        }}
                      >
                        {value.action_display_name}
                      </button>
                    </div>
                  );
                })}
                <ActionModal
                  setOpen={setOpen}
                  open={open}
                  formFields={actionButton}
                  shipmentData={shipmentTableData}
                  formId={formId}
                  setError={setError}
                />
              </div>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none"></div>
            <br></br>
            <div>
              <div className="sm:hidden">
                <label htmlFor="tabs" className="sr-only">
                  Select a tab
                </label>
                <select
                  id="tabs"
                  name="tabs"
                  className="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                >
                  {tabs.map((tab) => (
                    <option key={tab.name}>{tab.name}</option>
                  ))}
                </select>
              </div>
              <div className="hidden sm:block">
                <nav
                  className="isolate flex divide-x divide-gray-200 rounded-lg shadow"
                  aria-label="Tabs"
                >
                  {tabs.map((tab, tabIdx) => (
                    <a
                      onClick={() => {
                        changeTab(tab.name);
                      }}
                      key={tab.name}
                      className={
                        tab.current
                          ? "cursor-pointer text-gray-900 group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-center text-sm font-medium hover:bg-gray-50 focus:z-10"
                          : "cursor-pointer text-[#222222] hover:text-gray-700 group relative min-w-0 flex-1 overflow-hidden bg-white py-4 px-4 text-center text-sm font-medium hover:bg-gray-50 focus:z-10"
                      }
                    >
                      <span>{tab.name}</span>
                      <span
                        aria-hidden="true"
                        className={
                          tab.current
                            ? "bg-[#373435] absolute inset-x-0 bottom-0 h-0.5"
                            : "bg-transparent absolute inset-x-0 bottom-0 h-0.5"
                        }
                      />
                    </a>
                  ))}
                </nav>
              </div>
            </div>
            <br></br>
            {tabs[0].current ? (
              <div>
                <br></br>
                <div className="flex space text-center">
                  {/* <div className="flex-none w-28"></div> */}
                  <div className="w-full	shadow-lg rounded-2xl flex-1 bg-white px-4 py-8 sm:px-6 border-solid border-1">
                    <div className="w-full flex space-x-3">
                      <div className="w-[10%]">
                        <Image
                          src={summaryIcon}
                          width={50}
                          height={50}
                          alt="Summary Icon"
                        />
                      </div>
                      <div className="w-[90%] min-w-0 flex-1">
                        <h1 className="text-2xl text-left text-gray-900">
                          Shipment Summary
                        </h1>
                        <br></br>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-sm">
                              Status :{" "}
                            </span>
                            <span className="pl-8 w-[60%] block text-sm">
                              {summaryDataDetails.status}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-sm">
                              Tracking ID :{" "}
                            </span>
                            <span className="pl-8 w-[60%] block text-sm">
                              {summaryDataDetails.trackingID}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[40%] text-sm">
                              Tracking link :{" "}
                            </span>
                            <span className="pl-8 w-[60%] block text-sm">
                              {summaryDataDetails.trackingLink}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex-none w-5"></div>
                  <div className="flex-1 w-full bg-white shadow-lg px-4 py-8 sm:px-6 border-solid border-1 rounded-2xl">
                    <div className=" flex space-x-3">
                      <div className="w-[10%]">
                        <Image
                          src={distributorIcon}
                          width={50}
                          height={50}
                          alt="Distributor Icon"
                        />
                      </div>
                      <div className="w-[90%] min-w-0 flex-1">
                        <h1 className="text-2xl text-left text-gray-900">
                          Shipment Address
                        </h1>
                        <br></br>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[30%] text-sm">
                              Address :{" "}
                            </span>
                            <span className="pl-8 w-[70%] block text-sm">
                              {shippingAddress.address
                                ? shippingAddress.address
                                : "-"}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[30%] text-sm">
                              Pincode :{" "}
                            </span>
                            <span className="pl-8 w-[70%] block text-sm">
                              {shippingAddress.pincode
                                ? shippingAddress.pincode
                                : "-"}
                            </span>
                          </p>
                        </div>
                        <div className="flex pl-18">
                          <p className="text-gray-900 w-full text-left flex text-lg">
                            <span className="block w-[30%] text-sm">
                              Country :{" "}
                            </span>
                            <span className="pl-8 w-[70%] block text-sm">
                              {shippingAddress.country
                                ? shippingAddress.country
                                : "-"}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* <div className="flex-none w-28"></div> */}
                </div>
              </div>
            ) : (
              <div></div>
            )}
            <br></br>
            {tabs[0].current && (
              <div className="-mx-4 my-10 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                        >
                          Image
                        </th>
                        <th
                          scope="col"
                          className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                        >
                          SKU
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Budget Category
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Title
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Quantity
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Unit Price
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Total Price
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {detailsTableData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.image ? (
                              <img src={x.image} className="w-8 h-8" />
                            ) : (
                              ""
                            )}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.sku}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.metafieldValue}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.name}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.qty}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.price}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {calculateTotalPrice(x.price, x.qty)}
                            {/* {x.price} */}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
            {tabs[1].current && (
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                        >
                          Name
                        </th>
                        {/* <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Type
                        </th> */}
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Value
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {attributeData.map((x: any, i: any) => {
                        let displayValue;

                        // Check if the value is JSON and not null
                        if (x.type === "JSON" && x.value !== "null") {
                          try {
                            const parsedValue = JSON.parse(x.value);

                            // Check if parsedValue is an array
                            if (Array.isArray(parsedValue)) {
                              displayValue = (
                                <ul>
                                  {parsedValue.map((item: any, index: any) => (
                                    <li key={index}>
                                      {item.value.startsWith("http") ? (
                                        <a
                                          href={item.value}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="blue"
                                        >
                                          {item.input_name} -
                                          <span style={{ color: "blue" }}>
                                            {" "}
                                            Download
                                          </span>
                                        </a>
                                      ) : (
                                        `${item.input_name} - ${item.value}`
                                      )}
                                    </li>
                                  ))}
                                </ul>
                              );
                            } else {
                              displayValue = (
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(parsedValue, null, 2)}
                                </pre>
                              );
                            }
                          } catch (e) {
                            displayValue = (
                              <pre className="whitespace-pre-wrap">
                                {x.value}
                              </pre>
                            );
                          }
                        } else {
                          // If not JSON or if value is "null", just display "null"
                          displayValue = x.value === "null" ? "null" : x.value;
                        }

                        return (
                          <tr key={i} className="even:bg-gray-50">
                            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                              {x.name}
                            </td>
                            {/* <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                              {x.type}
                            </td> */}
                            <td className="whitespace-pre-wrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                              {displayValue}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* {tabs[1].current && (
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead>
                      <tr>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Internal Ref
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Items
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Created At
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Amount
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {shipmentTableData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.internalRef}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.status}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.items}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.createdAt}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.currency} {x.amount}
                          </td>
                          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                            <select
                              name="shipmentChange"
                              title="shipmentChange"
                              id="location"
                              className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                              <option>United States</option>
                              <option>Canada</option>
                              <option>Mexico</option>
                            </select>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )} */}
            {tabs[2].current && (
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Comments
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {attributeTableData.map((x: any, i: any) => (
                        <tr key={i} className="even:bg-gray-50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {new Date(x.time).toString().slice(0, 15) +
                              " " +
                              formatToAMPM(x.time)}
                          </td>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                            {x.comment}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>
      </Sidebar>
    </QueryClientProvider>
  );
}
