"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import Sidebar from "@/app/components/sidebar";
import { EllipsisVerticalIcon, PencilIcon } from "@heroicons/react/20/solid";
import config from "../../../config.json";
import CompanyModal from "../components/companyModal";
import { useDrag, useDrop, DndProvider, XYCoord } from 'react-dnd';
import { HTML5Backend } from "react-dnd-html5-backend";
import axios from "axios";
import Cookies from "js-cookie";

interface CompanyNote {
  _id: any;
  name: string;
  note: string;
}

interface ApiResponse {
  data: CompanyNote[];
}

const queryClient = new QueryClient();

export default function Company() {
  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState();
  const [addNewCompany, setAddNewCompany] = useState(false);

  const [data, setData] = useState<any>(null);

  const fetchData = useCallback(() => {
    const requestOptions = {
      method: "GET",
    };

    fetch(`${config.BACKEND_URL}/company`, requestOptions)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json();
      })
      .then((result) => {
        // console.log("result", result);
        setData({ data: result.data.data });
      })
      .catch((error) => {
        setData(null);
      });
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const moveRow = useCallback((dragIndex: any, hoverIndex) => {
    let newApiData = data?.data;
    const dragRow: any = newApiData[dragIndex];
    dragRow.priority = hoverIndex;
    const newData: any = [...newApiData];
    newData.splice(dragIndex, 1);
    newData.splice(hoverIndex, 0, dragRow);
    newData.forEach((row, index) => {
      row.priority = index;
    });
    setData({data: newData});
    updatePriorityList(newData);
  }, [data]);

  async function updatePriorityList(updatedData) {
    const filterDistributorData = updatedData.map((data) => { return {"_id": data._id, "priority": data.priority}});
    let response = await axios.request({
      method: "PATCH",
      url: `${config.BACKEND_URL}/company/priority/update`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${Cookies.get("token")}`,
      },
      data: JSON.stringify({"companies": filterDistributorData})
    });
  }


  const ItemType = 'row';

  const DraggableTableRow = ({ data, index, moveRow }) => {  
    const ref = useRef<HTMLTableRowElement>(null);
    const [, drop] = useDrop({
      accept: ItemType,
      hover(item: { type: string; index: number }, monitor) {
        if (!ref.current) {
          return;
        }
        const dragIndex = item.index;
        const hoverIndex = index;
  
        if (dragIndex === hoverIndex) {
          return;
        }
  
        const hoverBoundingRect = ref.current.getBoundingClientRect();
  
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
  
        const clientOffset = monitor.getClientOffset() as XYCoord;  
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;
  
        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return;
        }
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return;
        }
  
        moveRow(dragIndex, hoverIndex);
  
        item.index = hoverIndex;
      },
    });
  
    const [{ isDragging }, drag] = useDrag({
      type: ItemType,
      item: () => {
        return { type: ItemType, id: data.id, index };
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });
  
    drag(drop(ref));
  
    return (
      <>
        <tr ref={ref} key={data?._id} style={{ opacity: isDragging ? 0 : 1 }} className="even:bg-gray-50 cursor-all-scroll">
          <td className="w-6 mt-2 items-center h-[4.25rem] bg-[#80808030] flex">
            <EllipsisVerticalIcon className="h-full w-full ml-auto text-gray-400" />
          </td>

          <td className="whitespace-nowrap py-3.5 pl-8 pr-3 text-sm text-[#222222]">
            {index + 1}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {data?.name}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {data?.note}
          </td>
          <td className="relative text-left whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-6">
            <>
              <button
                type="button"
                className="rounded-full cursor-pointer bg-[#222222] p-2 text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                onClick={() => {
                  setOpen(!open);
                  setEdit(true);
                  setEditId(data?._id);
                }}
              >
                <PencilIcon
                  className="h-5 w-5"
                  aria-hidden="true"
                />
              </button>
            </>
          </td>
        </tr>
      </>
    );
  };
 
  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h1 className="text-2xl font-semibold leading-6 text-gray-900">
                Company
              </h1>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                onClick={() => {
                  setAddNewCompany(true);
                  setOpen(true);
                  setEdit(false);
                }}
              >
                Add Company
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <DndProvider backend={HTML5Backend}>
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th scope="col" className="rounded-tl-lg"></th>
                        <th
                          scope="col"
                          className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                        >
                          S. NO
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Notes
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {data != null && (
                        <>
                          {data?.data?.sort((a, b) => a.priority - b.priority).map((note, index) => (
                              <DraggableTableRow key={index} index={index} data={note} moveRow={moveRow} />
                          ))}
                        </>
                      )}
                    </tbody>
                  </table>
                  </DndProvider>
                </div>
              </div>
            </div>
          </div>
        </div>

        <CompanyModal
          open={open}
          setOpen={setOpen}
          updateCompany={edit}
          editId={editId}
          fetchData={fetchData}
          addNewCompany={addNewCompany}
        />
      </Sidebar>
    </QueryClientProvider>
  );
}
