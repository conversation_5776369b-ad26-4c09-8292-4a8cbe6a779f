import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';

export function Logout() {
  const router = useRouter();
  const logout = useCallback((event: React.MouseEvent<HTMLParagraphElement>) => {
    event.preventDefault(); 
    Cookies.set("token", "");
    Cookies.set("type", "");
    Cookies.set("name", "");
    router.push('/login');
  }, [router]);

  return logout;
}
