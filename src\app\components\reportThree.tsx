import { ArrowDownIcon, ArrowUpIcon } from "@heroicons/react/20/solid";
import { useEffect, useState } from "react";
import config from "../../../config.json";

const stats = [
  {
    name: "Total Non-Aligned Shipments",
    stat: "49",
    previousStat: "24",
    change: "104.16%",
    changeType: "increase",
  },
  {
    name: "Fulfilled using Domestic Divergence",
    stat: "51",
    previousStat: "57",
    change: "10.52%",
    changeType: "decrease",
  },
  {
    name: "Pending",
    stat: "26",
    previousStat: "19",
    change: "36.84%",
    changeType: "increase",
  },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function ReportThree({ type, title }) {
  console.log("---->ReportThree", type);
  const [apiData, setapiData] = useState<any>([]);

  useEffect(() => {
    const requestOptions = {
      method: "GET",
    };

    fetch(`${config.BACKEND_URL}/${type}`, requestOptions)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json();
      })
      .then((result) => {
        setapiData(result);
      })
      .catch((error) => {
        setapiData(null);
      });
  }, []);

  return (
    <div>
      <h3 className="text-base font-semibold leading-6 text-gray-900">
        {title}
      </h3>
      <dl className="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-3 md:divide-x md:divide-y-0">
        {apiData?.map((item) => (
          <div key={item.type} className="px-4 py-5 sm:p-6">
            <dt className="text-base font-normal text-gray-900">{item.type}</dt>
            <dd className="mt-1 flex items-baseline justify-between md:block lg:flex">
              <div className="flex items-baseline text-2xl font-semibold text-indigo-600">
                {item.count}
                <span className="ml-2 text-sm font-medium text-[#222222]">
                  from {item.previousCount}
                </span>
              </div>

              <div
                className={classNames(
                  item.changeType === "increased"
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800",
                  "inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0"
                )}
              >
                {item.changeType === "increased" ? (
                  <ArrowUpIcon
                    className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-green-500"
                    aria-hidden="true"
                  />
                ) : (
                  <ArrowDownIcon
                    className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-red-500"
                    aria-hidden="true"
                  />
                )}

                <span className="sr-only">
                  {" "}
                  {item.changeType === "increased"
                    ? "Increased"
                    : "Decreased"}{" "}
                  by{" "}
                </span>
                {`${item.percentageChange.toFixed(2)} %`}
              </div>
            </dd>
          </div>
        ))}
      </dl>
    </div>
  );
}
