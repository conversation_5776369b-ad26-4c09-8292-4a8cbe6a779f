"use client";
import React, { FC, useState, useEffect, useRef, Fragment, useCallback } from "react";
import Sidebar from "../components/sidebar";
import axios from "axios";
import config from "../../../config.json";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import { useDrag, useDrop, DndProvider, XYCoord } from 'react-dnd';
import { HTML5Backend } from "react-dnd-html5-backend";
import { DeleteConfirmationModal } from "../components/deleteConfirmationModal";
import UpdateDistributorForm from "../components/updateDistributorForm";
import { EllipsisVerticalIcon } from "@heroicons/react/20/solid";
import { PencilIcon, TrashIcon } from '@heroicons/react/20/solid'

interface RowData {
  id: string;
  priority: string;
  name: string;
  shopifyCompany: string;
  email: string;
  country: string;
}

interface ShopifyGroup {
  id: string;
  name: string;
}

const queryClient = new QueryClient();



export default function Distributors() {
  const [tableData, setTableData] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [error, setError] = useState<any>();
  const cancelButtonRef = useRef(null);
  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [shopifyCompanyData, setShopifyCompanyData] = useState<ShopifyGroup[]>([]);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState("");

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");


  function deleteDistributor(id: any) {
    setCurrentId(id);
    setCurrentType("distributor");
    setConfirmationOpen(true);
  }

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
  };

  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
  };

  async function sendDistributorCreation() {
    try {
      if (password !== confirmPassword) {
        setError('Passwords do not match');
        return;
      }


      let response = await fetch(`${config.BACKEND_URL}/distributor`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          firstName: (document.getElementById("firstName") as HTMLFormElement)?.value,
          lastName: (document.getElementById("lastName") as HTMLFormElement)?.value,
          password: (document.getElementById("password") as HTMLFormElement)?.value,
          name: (document.getElementById("firstName") as HTMLFormElement)?.value + " " + (document.getElementById("lastName") as HTMLFormElement)?.value,
          shopifyCompanyId: (document.getElementById("shopifyCompanyId") as HTMLFormElement)?.value,
          email: (document.getElementById("email") as HTMLFormElement)?.value,
          country: (document.getElementById("country") as HTMLFormElement)?.value,
          internalCode: (document.getElementById("internalCode") as HTMLFormElement)?.value,
          customerSince: (document.getElementById("customerSince") as HTMLFormElement)?.value,
          externalId: (document.getElementById("externalId") as HTMLFormElement)?.value,
          note: (document.getElementById("note") as HTMLFormElement)?.value,
          phone: (document.getElementById("phone") as HTMLFormElement)?.value,
          priority: tableData.length
        }),
      });

      let result = await response.json();
      const data = await fetchDistributorData();
      setTableData(data);
      setError(result.message);
      setOpen(false);
    } catch (error) {
      setError(error);
    }
  }

  async function fetchShopifyCompanyData() {
    let response = await axios.request({
      method: "post",
      url: `${config.BACKEND_URL}/shopify/company`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    setShopifyCompanyData(response.data.data);
  }

  async function fetchDistributorData() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/distributor`,
      headers: {
        "Content-Type": "application/json",
      },
    });

    let data = response.data.data.data.map((x: any) => {
      return {
        id: x._id,
        name: x.firstName != undefined ? x.firstName + " " + x.lastName : x.name,
        shopifyCompany: x.shopifyCompanyId,
        email: x.email,
        priority: x.priority,
        country: x.country,
        internalCode: x.internalCode,
      };
    });

    return data;
  }

  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchDistributorData();
      setTableData(data);
    };

    fetchData();    
    fetchShopifyCompanyData();
  }, []);

  const moveRow = useCallback((dragIndex: number, hoverIndex: number) => {
    const newData = [...tableData];
    const [draggedRow] = newData.splice(dragIndex, 1);
    newData.splice(hoverIndex, 0, draggedRow);
    newData.forEach((row, index) => {
      row.priority = index;
    });
    setTableData(newData);
    updatePriorityList(newData);
  }, [tableData]);

  async function updatePriorityList(updatedData) {
    const filterDistributorData = updatedData.map((data) => { return { "_id": data.id, "priority": data.priority } });
    await axios.request({
      method: "PATCH",
      url: `${config.BACKEND_URL}/distributor/priority/update`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({ "distributors": filterDistributorData })
    });
  }

  


  const DraggableTableRow = ({ data, index, moveRow }) => {
    const ref = useRef<HTMLTableRowElement>(null);
  
    const [, drop] = useDrop({
      accept: 'row',
      hover(item: { type: string; index: number }, monitor) {
        if (!ref.current) {
          return;
        }
        const dragIndex = item.index;
        const hoverIndex = index;
  
        if (dragIndex === hoverIndex) {
          return;
        }
  
        const hoverBoundingRect = ref.current.getBoundingClientRect();
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
        const clientOffset = monitor.getClientOffset() as XYCoord;
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;
  
        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return;
        }
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return;
        }
  
        moveRow(dragIndex, hoverIndex);
        item.index = hoverIndex;
      },
    });
  
    const [{ isDragging }, drag] = useDrag({
      type: 'row',
      item: { type: "row", id: data.id, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });
  
    drag(drop(ref));
  
    const company = shopifyCompanyData.find((value) => {return value.id == data.shopifyCompany});

    return (
      <tr ref={ref} key={data.id} style={{ opacity: isDragging ? 0 : 1 }} className="even:bg-gray-50 cursor-all-scroll">
        <td className="w-6 items-center h-[4.25rem] bg-[#80808030] flex">
          <EllipsisVerticalIcon className="w-full ml-auto text-gray-400" />
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.priority + 1}
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.name}
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
        {company ? company.name : ""}
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.email}
        </td>
        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          {data.country}
        </td>
        <td className="relative flex justify-evenly whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
          
          
          <button
            onClick={() => {
              setEditOpen(!open);
              setEdit(true);
              setEditId(data?.id);
            }}
            type="button"
            className="block rounded-full bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">
            <PencilIcon className="h-5 w-5" aria-hidden="true" />
          </button>
          <button
            onClick={() => deleteDistributor(data.id)}
            type="button"
            className="block rounded-full ml-4 bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">
            <TrashIcon className="h-5 w-5" aria-hidden="true" />
          </button>
        </td>
      </tr>
    );
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            className="relative z-10"
            initialFocus={cancelButtonRef}
            onClose={setOpen}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0">
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>
            <div className="fixed inset-0 z-10 w-screen overflow-y-scroll pt-12">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                  <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div>
                      <div className="mt-3 text-center sm:mt-5">
                        <div className="isolate -space-y-px rounded-md shadow-sm">
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              First Name
                            </label>
                            <input
                              type="text"
                              name="firstName"
                              id="firstName"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Last Name
                            </label>
                            <input
                              type="text"
                              name="lastName"
                              id="lastName"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Phone
                            </label>
                            <input
                              type="number"
                              name="phone"
                              id="phone"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="password"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Password
                            </label>
                            <input
                              type="password"
                              name="password"
                              id="password"
                              onChange={handlePasswordChange}
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="password"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Confirm Password
                            </label>
                            <input
                              type="password"
                              name="confirmPassword"
                              id="password"
                              onChange={handleConfirmPasswordChange}
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            {shopifyCompanyData &&
                              <select
                                name="shopifyCompanyId"
                                id="shopifyCompanyId"
                                required
                                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              >
                                <option value="">Select a Company</option>
                                {shopifyCompanyData && shopifyCompanyData.map((group, index) => (
                                  <option value={group.id} key={index}>{group.name}</option>
                                ))}
                              </select>
                            }
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="email"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Email
                            </label>
                            <input
                              type="email"
                              name="email"
                              id="email"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="country"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Country
                            </label>
                            <input
                              type="text"
                              name="country"
                              id="country"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="note"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Note
                            </label>
                            <input
                              type="text"
                              name="note"
                              id="note"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
                    <div className="mt-5 sm:mt-6 flex justify-center sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-[30%] justify-center rounded-md border-[#1D1C21] text-[#1D1C21] border-2 px-3 py-2 text-sm font-semibol shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2"
                        onClick={() => {
                          setOpen(false);
                        }}>
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="inline-flex w-[30%] justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2"
                        onClick={() => {
                          sendDistributorCreation();
                          
                        }}>
                        Create
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>
        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto"></div>
            {error && !error.includes("Passwords do not match") && <p className="text-red-600 font-bold">{error}</p>}
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                onClick={() => { setOpen(true); }}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2">
                Create distributor
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <DndProvider backend={HTML5Backend}>
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th scope="col" className="rounded-tl-lg"></th>
                        <th
                          scope="col"
                          className=" px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Priority
                        </th>
                        <th
                          scope="col"
                          className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Company
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Email
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Country
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {tableData && tableData?.sort((a, b) => a.priority - b.priority).map((data, index) => (
                        <>
                          {data && <DraggableTableRow key={data.id} index={index} data={data} moveRow={moveRow} />}
                        </>
                      ))}
                    </tbody>
                  </table>
                </DndProvider>
              </div>
            </div>
          </div>
        </div>
        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchDistributorData}
            setTableData={setTableData}
          />
        )}


        {editOpen && <UpdateDistributorForm
          editId={editId}
          fetchCompanyData = {fetchDistributorData}
          setOpen = {setEditOpen}
          open={editOpen}
          shopifyCompanyData={shopifyCompanyData}
          setError={setError}
          setTableData={setTableData}
        />}
      </Sidebar>
    </QueryClientProvider>
  );
}
