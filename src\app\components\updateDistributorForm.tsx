import { useMutation, useQuery } from "@tanstack/react-query";
import React, { Fragment, useCallback, useRef, useState } from "react";
import config from "../../../config.json";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/20/solid";

type FormData = {
  name: string;
  note: string;
};

export default function UpdateDistributorForm({
  editId,
  fetchCompanyData,
  setOpen,
  open,
  shopifyCompanyData,
  setError,
  setTableData
}: {
  editId: string;
  fetchCompanyData: any;
  setOpen: any;
  open: boolean;
  shopifyCompanyData: any,
  setError: any,
  setTableData: any
}) {
  const updateCompany = useMutation({
    mutationFn: async (updatedData: FormData) => {
      const requestOptions: RequestInit = {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedData),
      };

      const response = await fetch(
        `${config.BACKEND_URL}/distributor/${editId}`,
        requestOptions
      );
      const data = await fetchCompanyData();
      setTableData(data);
      setOpen(false);
      const finalResponse = await response.json();
      if(finalResponse.status == "fail") {
        setError(finalResponse.message);
      }
      return finalResponse;
    },
  });
  const [formData, setFormData] = useState<any>({});
  const [changedData, setChangedData] = useState<any>({});

  const fetchData = useCallback(async (id: string) => {
    const response = await fetch(`${config.BACKEND_URL}/distributor/${id}`);
    if (!response.ok) throw new Error("Network response was not ok");
    const res = await response.json();
    console.log(res, " ressssss");
    setFormData(res?.data?.data);
    return res;
  }, []);

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ["users", editId],
    queryFn: () => fetchData(editId),
  });

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data.</div>;

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setChangedData({
      [name]: value,
    })
  };

  const handleSubmit = (event) => {
    event.preventDefault();
   
    updateCompany.mutate(changedData);
  };


  return (
    <Transition show={open}>
      <Dialog className="relative z-10" onClose={setOpen}>
        <Transition.Child
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-scroll pt-12">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
           
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <form onSubmit={handleSubmit}>
                  <div className="space-y-6">
                    <div>
                      <h2 className="text-center font-semibold leading-7 text-gray-900">
                        Update Distributor
                      </h2>
                    </div>

                    <div className="pb-6">
                      <div className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-1">
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="firstName"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            First Name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="firstName"
                              id="firstName"
                              autoComplete="off"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              value={formData.firstName || ""}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="firstName"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Last Name
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="lastName"
                              id="lastName"
                              autoComplete="off"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              value={formData.lastName || ""}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="email"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Email
                          </label>
                          <div className="mt-2">
                            <input
                              type="email"
                              name="email"
                              id="email"
                              autoComplete="off"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              value={formData.email || ""}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="email"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Company
                          </label>
                          <div className="mt-2">
                            {shopifyCompanyData &&
                              <select
                                name="shopifyCompanyId"
                                id="shopifyCompanyId"
                                defaultValue={formData.shopifyCompanyId || ""}
                                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              >
                                <option value="">Select a Company</option>
                                {shopifyCompanyData.map((group, index) => (
                                  <option value={group.id} key={index}>{group.name}</option>
                                ))}
                              </select>
                            }
                          </div>
                        </div>
                        <div className="sm:col-span-3">
                          <label
                            htmlFor="phone"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Phone
                          </label>
                          <div className="mt-2">
                            <input
                              type="number"
                              name="phone"
                              id="phone"
                              autoComplete="off"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              value={formData.phone || ""}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>

                        <div className="sm:col-span-3">
                          <label
                            htmlFor="note"
                            className="block text-sm font-medium leading-6 text-gray-900"
                          >
                            Note
                          </label>
                          <div className="mt-2">
                            <input
                              type="text"
                              name="note"
                              id="note"
                              autoComplete="off"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              value={formData.note || ""}
                              onChange={handleInputChange}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex items-center justify-center gap-x-6">
                    <button
                      type="button"
                      className="text-sm font-semibold w-[30%] leading-6 text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
                      onClick={() => setOpen(false)}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="rounded-md w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                    >
                      Save
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
