import React from "react";

interface CommentModalProps {
  title: string; // Title of the modal
  message: string; // Message to display
  onClose: () => void; // Close handler
  onConfirm: () => void | Promise<void>; // Confirm handler
  confirmLoading?: boolean;
  children?: React.ReactNode; // Optional children
}

const CommentModal: React.FC<CommentModalProps> = ({
  title,
  message,
  onClose,
  onConfirm,
  children,
  confirmLoading,
}) => {
  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded shadow-lg w-[31%] max-w-screen-md">
        <h2 className="text-lg font-bold mb-4 text-black">{title}</h2>
        <div className="mb-4 text-black">{message}</div>
        {children && <div className="mb-4">{children}</div>}
        <div className="flex justify-end mt-4 space-x-2">
          <button
            onClick={onClose}
            className="bg-gray-500 text-white py-2 px-4 rounded w-[85px] flex items-center justify-center"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={confirmLoading}
            className={`bg-gray-700 text-white py-2 px-4 rounded flex items-center justify-center w-[85px] ${
              confirmLoading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {confirmLoading ? (
              <svg
                className="animate-spin h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="4"
                  strokeLinecap="round"
                  d="M22 12a10 10 0 00-10-10"
                />
              </svg>
            ) : (
              "Confirm"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CommentModal;
