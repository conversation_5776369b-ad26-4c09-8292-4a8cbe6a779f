import RolesPage from './RolesPage';
import config from "../../../../config.json";
import { cookies } from 'next/headers';

export default async function GetRolePageData() {
  const cookieStore = cookies();
  const token = cookieStore.get('token')?.value;

  const requestOptions = {
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };

  const response = await fetch(`${config.BACKEND_URL}/usergroup`, requestOptions);
  const data = await response.json();
  
  return <RolesPage pageData={data} />;
}
