"use client";

import { Fragment, useEffect, useState } from "react";
import { <PERSON>alog, Menu, Transition } from "@headlessui/react";
import {
  Bars3Icon,
  BellIcon,
  CalendarIcon,
  ChartPieIcon,
  Cog6ToothIcon,
  DocumentDuplicateIcon,
  FolderIcon,
  HomeIcon,
  UsersIcon,
  XMarkIcon,
  BuildingOffice2Icon,
  UserIcon,
  BuildingOfficeIcon,
  InboxIcon,
  TruckIcon,
  CheckBadgeIcon,
  ShoppingCartIcon,
  CheckCircleIcon,
  BriefcaseIcon,
  ClipboardDocumentIcon,
  IdentificationIcon,
  PencilSquareIcon,
} from "@heroicons/react/24/outline";
import {
  ChevronDownIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/20/solid";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Logo from "../assets/Sunrise-logo.png";
import SunriseWhite from "../assets/Sunrise_TradeWhite.png";
import { useRouter } from "next/router";
// import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import Cookies from "js-cookie";
import { Logout } from "../utils/auth/logout";

// const queryClient = new QueryClient();

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

type AccessScopes = {
  [key: string]: string[];
};

export default function Sidebar({ children }: { children: React.ReactNode }) {
  const router = usePathname();
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    const role = Cookies.get("userrole");
    setUserRole(role || null);
  }, []);

  console.log("--user", userRole);

  type NavigationItem = {
    name: string;
    href: string;
    icon: React.ForwardRefExoticComponent<React.SVGProps<SVGSVGElement>>;
    current: boolean;
    noniterable?: boolean;
    subItem?: Array<{
      name: string;
      href: string;
      icon: React.ForwardRefExoticComponent<React.SVGProps<SVGSVGElement>>;
      current: boolean;
    }>;
  }
  
  // Add this type definition after your AccessScopes type and before the navigation array
  const navigation: NavigationItem[] = [
    {
      name: "Home",
      href: "/",
      icon: HomeIcon,
      current: router == "/" ? true : false,
    },
    {
      name: "Order",
      href: "/orders",
      icon: ShoppingCartIcon,
      current: router == "/orders" ? true : false,
      noniterable:
        userRole === "Finance Manager" || userRole === "Finance Executive"
          ? true
          : false,
    },
    // {
    //   name: "Shipment",
    //   href: "/shipments",
    //   icon: TruckIcon,
    //   current: router == "/shipments" ? true : false,
    //   noniterable:
    //     userRole === "Finance Manager" || userRole === "Finance Executive"
    //       ? true
    //       : false,
    // },
    {
      name: "Customers",
      href: "/customers",
      icon: DocumentDuplicateIcon,
      current: router == "/customers" ? true : false,
      noniterable: userRole === "Sunrise Team" ? true : false,
    },
    {
      name: "Products",
      href: "/productsync",
      icon: DocumentDuplicateIcon,
      current: router == "/productsync",
      noniterable: userRole === "Sunrise Team",
    },
    {
      name: "Profile",
      href: "/profile",
      icon: UserIcon,
      current: router == "/profile" ? true : false,
      // noniterable: userRole === "Sunrise Team" ? true : false,
    },
    {
      // name: "Organization",
      // href: "/organization/designation",
      // subItem: [
      //   {
      //     name: "Designation",
      //     href: "/organization/designation",
      //     icon: IdentificationIcon,
      //     current: router == "/organization/designation" ? true : false,
      //   },
      //   {
      //     name: "Department",
      //     href: "/organization/department",
      //     icon: BriefcaseIcon,
      //     current: router == "/organization/department" ? true : false,
      //   },
      //   {
      //     name: "People",
      //     href: "/organization/people",
      //     icon: UsersIcon,
      //     current: router == "/organization/people" ? true : false,
      //   },
      // ],
      // icon: BuildingOfficeIcon,
      // current: router == "/organization/designation" ? true : false,
      name: "People",
      href: "/organization/people",
      icon: UsersIcon,
      current: router == "/organization/people" ? true : false,
    },
    {
      name: "User",
      href: "/users/people",
      // subItem: [
      //   // {
      //   //   name: "Roles",
      //   //   href: "/users/roles",
      //   //   icon: BriefcaseIcon,
      //   //   current: router == "/users/roles" ? true : false,
      //   // },
      //   {
      //     name: "Users",
      //     href: "/users/people",
      //     icon: UsersIcon,
      //     current: router == "/users/people" ? true : false,
      //   },
      // ],
      icon: UsersIcon,
      current: router == "/users/people" ? true : false,
    },
    // {
    //   name: "Status",
    //   href: "/status",
    //   icon: ClipboardDocumentIcon,
    //   current: router == "/status" ? true : false,
    // },
    // {
    //   name: "Company",
    //   href: "/company",
    //   icon: BuildingOffice2Icon,
    //   current: router == "/company" ? true : false,
    // },
  ];

  const userNavigation = [{ name: "Sign out", href: "#" }];

  const logout = Logout();

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [accessScopes, setAccessScopes] = useState({});
  const [userName, setUserName] = useState("User");

  const [dropdownOpen, setDropdownOpen] = useState(
    Array(navigation.length).fill(false)
  );

  const toggleDropdown = (index) => {
    setDropdownOpen((prev) => {
      const newDropdownOpen = [...prev];
      newDropdownOpen[index] = !newDropdownOpen[index];
      return newDropdownOpen;
    });
  };

  useEffect(() => {
    setUserName(Cookies.get("name"));
    if (Cookies.get("type")) {
      const scopes = JSON.parse(Cookies.get("type"));

      const accessScopes = {
        home: ["read", "write", "delete"],
        customers: ["read", "write", "delete"],
        products: ["read", "write", "delete"],
      };

      const combinedScopes = {
        ...scopes,
        ...accessScopes,
      };

      setAccessScopes(combinedScopes);
    } else {
      setAccessScopes({
        home: ["read", "write", "delete"],
        order: ["read", "write", "delete"],
        shipment: ["read", "write", "delete"],
        customers: ["read", "write", "delete"],
        profile: ["read", "write", "delete"],
      });
    }
  }, []);

  const hasAccess = (key) => {
    return accessScopes[key]?.length > 0;
  };

  const filteredNavigation = navigation.filter((item) => !item.noniterable);

  return (
    <div>
      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50 lg:hidden"
          onClose={setSidebarOpen}
        >
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-[#373435]" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  as={Fragment}
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button
                      type="button"
                      className="-m-2.5 p-2.5"
                      onClick={() => setSidebarOpen(false)}
                    >
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon
                        className="h-6 w-6 text-white"
                        aria-hidden="true"
                      />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-[#222222] px-6 pb-4 ring-1 ring-white/10">
                  <div className="flex shrink-0 items-center justify-center">
                    <img
                      className="h-auto w-24 p-2"
                      src={SunriseWhite.src}
                      alt="Your Company"
                    />
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {accessScopes &&
                            filteredNavigation.map((item, index) => {
                              const scopeKey = item.name.toLowerCase();
                              const subItemAccess = item.subItem
                                ? item.subItem.some((subItem) =>
                                    hasAccess(subItem.name.toLowerCase())
                                  )
                                : false;
                              if (
                                hasAccess(scopeKey) ||
                                subItemAccess ||
                                !Cookies.get("type")
                              ) {
                                return (
                                  <li key={index}>
                                    {item.subItem ? (
                                      <>
                                        <span
                                          onClick={() => toggleDropdown(index)}
                                          className={classNames(
                                            item.current
                                              ? "bg-gray-800 text-white"
                                              : "hover:bg-gray-800",
                                            "group cursor-pointer flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-white"
                                          )}
                                        >
                                          <item.icon
                                            className="h-6 w-6 shrink-0"
                                            aria-hidden="true"
                                          />
                                          {item.name}
                                          <ChevronDownIcon
                                            className="h-5 w-5 ml-auto text-white"
                                            aria-hidden="true"
                                          />
                                        </span>
                                        {dropdownOpen[index] && (
                                          <ul
                                            role="list"
                                            className="mx-4 pt-2 space-y-1"
                                          >
                                            {item.subItem.map(
                                              (subItem, subIndex) => (
                                                <>
                                                  {item.name.toLowerCase() ==
                                                    "user" ||
                                                  hasAccess(
                                                    subItem.name.toLowerCase()
                                                  ) ? (
                                                    <li
                                                      key={`${subItem.name}-${subIndex}`}
                                                    >
                                                      <Link
                                                        href={subItem.href}
                                                        className={classNames(
                                                          subItem.current
                                                            ? "bg-gray-800 text-white"
                                                            : "hover:bg-gray-800",
                                                          "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-white"
                                                        )}
                                                      >
                                                        <subItem.icon
                                                          className="h-6 w-6 shrink-0"
                                                          aria-hidden="true"
                                                        />
                                                        {subItem.name}
                                                      </Link>
                                                    </li>
                                                  ) : (
                                                    ""
                                                  )}
                                                </>
                                              )
                                            )}
                                          </ul>
                                        )}
                                      </>
                                    ) : (
                                      <Link
                                        href={item.href}
                                        className={classNames(
                                          item.current
                                            ? "bg-gray-800 text-white"
                                            : "text-white hover:text-white hover:bg-gray-800",
                                          "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold"
                                        )}
                                      >
                                        <item.icon
                                          className="h-6 w-6 shrink-0"
                                          aria-hidden="true"
                                        />
                                        {item.name}
                                      </Link>
                                    )}
                                  </li>
                                );
                              }
                              return null;
                            })}
                        </ul>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-56 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-[#222222] px-6 pb-4">
          <div className="flex shrink-0 items-center justify-center">
            <img
              className="h-auto w-30 p-2"
              src={SunriseWhite.src}
              alt="Your Company"
            />
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {accessScopes &&
                    filteredNavigation.map((item, index) => {
                      const scopeKey = item.name.toLowerCase();
                      const subItemAccess = item.subItem
                        ? item.subItem.some((subItem) =>
                            hasAccess(subItem.name.toLowerCase())
                          )
                        : false;
                      if (hasAccess(scopeKey) || subItemAccess) {
                        return (
                          <li key={index}>
                            {item.subItem ? (
                              <>
                                <span
                                  onClick={() => toggleDropdown(index)}
                                  className={classNames(
                                    item.current
                                      ? "bg-gray-800 text-white"
                                      : "hover:bg-gray-800",
                                    "group cursor-pointer flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-white"
                                  )}
                                >
                                  <item.icon
                                    className="h-6 w-6 shrink-0"
                                    aria-hidden="true"
                                  />
                                  {item.name}
                                  <ChevronDownIcon
                                    className="h-5 w-5 ml-auto text-white"
                                    aria-hidden="true"
                                  />
                                </span>

                                {dropdownOpen[index] && (
                                  <ul
                                    role="list"
                                    className="mx-4 pt-2 space-y-1"
                                  >
                                    {item.subItem.map((subItem, subIndex) => {
                                      // const subItemScopeKey = subItem.name.toLowerCase();
                                      // if (hasAccess(subItemScopeKey)) {
                                      return (
                                        <>
                                          {item.name.toLowerCase() == "user" ||
                                          hasAccess(
                                            subItem.name.toLowerCase()
                                          ) ? (
                                            <li
                                              key={`${subItem.name}-${subIndex}`}
                                            >
                                              <Link
                                                href={subItem.href}
                                                className={classNames(
                                                  subItem.current
                                                    ? "bg-gray-800 text-white"
                                                    : "hover:bg-gray-800",
                                                  "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-white"
                                                )}
                                              >
                                                <subItem.icon
                                                  className="h-6 w-6 shrink-0"
                                                  aria-hidden="true"
                                                />
                                                {subItem.name}
                                              </Link>
                                            </li>
                                          ) : (
                                            ""
                                          )}
                                        </>
                                      );
                                      // }
                                      // return null;
                                    })}
                                  </ul>
                                )}
                              </>
                            ) : (
                              <Link
                                href={item.href}
                                className={classNames(
                                  item.current
                                    ? "bg-gray-800 text-white"
                                    : "text-white hover:text-white hover:bg-gray-800",
                                  "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold"
                                )}
                              >
                                <item.icon
                                  className="h-6 w-6 shrink-0"
                                  aria-hidden="true"
                                />
                                {item.name}
                              </Link>
                            )}
                          </li>
                        );
                      }
                      return null;
                    })}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      <div className="lg:pl-56">
        <div className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-[#222222] px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
          <button
            type="button"
            className="-m-2.5 p-2.5 text-gray-700 lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <Bars3Icon className="h-6 w-6" aria-hidden="true" />
          </button>
          <div
            className="h-6 w-px bg-gray-900/10 lg:hidden"
            aria-hidden="true"
          />
          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between">
            <div className="block h-full w-auto"></div>
            <div className="flex items-center gap-x-4 lg:gap-x-6">
              <Menu as="div" className="relative">
                <Menu.Button className="-m-1.5 flex items-center p-1.5">
                  <span className="sr-only">Open user menu</span>
                  <img
                    className="h-8 w-8 rounded-full bg-gray-50"
                    src="https://ps.w.org/user-avatar-reloaded/assets/icon-128x128.png?rev=2540745"
                    alt=""
                  />
                  <span className="hidden lg:flex lg:items-center">
                    <span
                      className="ml-4 text-sm font-semibold leading-6 text-white"
                      aria-hidden="true"
                    >
                      {userName}
                    </span>
                    <ChevronDownIcon
                      className="ml-2 h-5 w-5 text-white"
                      aria-hidden="true"
                    />
                  </span>
                </Menu.Button>
                <Transition
                  as={Fragment}
                  enter="transition ease-out duration-100"
                  enterFrom="transform opacity-0 scale-95"
                  enterTo="transform opacity-100 scale-100"
                  leave="transition ease-in duration-75"
                  leaveFrom="transform opacity-100 scale-100"
                  leaveTo="transform opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                    {userNavigation.map((item, index) => (
                      <Menu.Item key={index}>
                        {({ active }) => (
                          <>
                            {item.name === "Sign out" ? (
                              <p
                                onClick={logout}
                                className="cursor-pointer block px-3 py-1 text-sm leading-6 text-gray-900"
                              >
                                {item.name}
                              </p>
                            ) : (
                              <a
                                href={item.href}
                                className={classNames(
                                  active ? "bg-gray-50" : "",
                                  "block px-3 py-1 text-sm leading-6 text-gray-900"
                                )}
                              >
                                {item.name}
                              </a>
                            )}
                          </>
                        )}
                      </Menu.Item>
                    ))}
                  </Menu.Items>
                </Transition>
              </Menu>
            </div>
          </div>
        </div>
        <main className="h-auto min-h-screen bg-[#efefef]">
          <div className={router === "/" ? "px-0" : "px-4 sm:px-6 lg:px-6"}>{children}</div>
        </main>
      </div>
    </div>
  );
}
