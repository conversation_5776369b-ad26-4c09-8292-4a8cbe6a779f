import { Fragment, useEffect, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import config from "../../../config.json";
import Cookies from "js-cookie";
import axios from "axios";

export default function ActionModal({
  setOpen,
  shipmentData,
  open,
  formFields,
  formId,
  setError,
}) {
  const [selectedFileTypes, setSelectedFileTypes] = useState<string[]>([]);
  const [priceError, setPriceError] = useState<any>();

  const handleCheckboxChange = (event) => {
    const { value, checked } = event.target;

    if (checked) {
      setSelectedFileTypes((prev) => [...prev, value]);
    } else {
      setSelectedFileTypes((prev) => prev.filter((type) => type !== value));
    }
  };

  const data = formFields.filter((field) => field._id == formId) || [];

  const totalAmount = shipmentData?.lineItems?.reduce(
    (sum, item) => sum + item.requested * item.price,
    0
  );

  async function updateShipment(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();

    const formData = new FormData(event.currentTarget);
    const hasFile = Array.from(formData.values()).some(
      (value) => value instanceof File
    );
    const getFile = Array.from(formData.values()).filter(
      (value) => value instanceof File
    );
    const json = Object.fromEntries(formData.entries());
    let form_Data: any = [];
    let lastInputId: string | null = null;

    Array.from(formData.entries()).forEach((entry) => {
      const [input_id, value] = entry;

      // Skip unwanted fields
      if (
        input_id === "action_link_id" ||
        input_id === "actionRoute" ||
        input_id === "action_unique_key"
      ) {
        return;
      }

      // Only add to form_Data if input_id is not a repeated field
      if (input_id !== lastInputId) {
        // If input_id matches the pattern you want to skip, continue to the next iteration
        if (input_id === "input_id") {
          lastInputId = input_id; // Still update lastInputId to skip this field
          return;
        }

        let newObj = {
          input_id,
          value,
        };
        form_Data.push(newObj);
      }

      // Update lastInputId to current input_id
      lastInputId = input_id;
    });

    const slaIds = data.flatMap((action) =>
      action.formData
        .filter((formItem) => formItem.input_name.includes("Enter SLA"))
        .map((formItem) => formItem._id)
    );

    const matchedValues = form_Data
      .filter((item) => slaIds.includes(item.input_id))
      .map((item) => item.value);

    const value = {
      action_link_id: json.action_link_id,
      shipment_id: shipmentData._id,
      form_data: form_Data,
    };

    if (formData.get("action_unique_key") == "download_shipment_items") {
      try {
        let url = `${config.BACKEND_URL}/shipment/download_shipment_items?shipment_id=${shipmentData._id}`;

        const response = await fetch(url, {
          method: "GET",
        });

        if (!response.ok) {
          const errorData = await response.json();

          setError(
            errorData.errors[0].message || "An unexpected error occurred."
          );
          setOpen(false);
          return;
        }
        window.open(url, "_blank");
      } catch (error) {
        setError("An unexpected error occurred.");
      }
      setOpen(false);
      return;
    }

    if (formData.get("action_unique_key") == "generate_pi") {
      let url = `${config.BACKEND_URL}/shipment/generate_pi?shipment_id=${shipmentData._id}`;
      fetch(`${url}`)
        .then((response) => response.json())
        .then((result) => {
          if (
            selectedFileTypes.includes("sheet") &&
            !selectedFileTypes.includes("pdf")
          ) {
            window.open(result.data.sheetDownloadUrl, "_blank");
          }
          if (
            selectedFileTypes.includes("pdf") &&
            !selectedFileTypes.includes("sheet")
          ) {
            window.open(result.data.pdfDownloadUrl, "_blank");
          }

          if (
            selectedFileTypes.includes("pdf") &&
            selectedFileTypes.includes("sheet")
          ) {
            window.open(result.data.sheetDownloadUrl, "_blank");
            window.open(result.data.pdfDownloadUrl, "_blank");
          }

          setOpen(false);
        })
        .catch((error) => console.error(error));

      return;
    }

    if (formData.get("action_unique_key") == "change_status_only") {
      let url = `${config.BACKEND_URL}${formData.get("actionRoute")}`;

      const salesOrderPriceField = formFields
        .flatMap((field) => field.formData)
        .find((data) => data.priceValidation === true);

      if (salesOrderPriceField) {
        const salesOrderPriceValue = form_Data.find(
          (item) => item?.input_id === salesOrderPriceField?._id
        );

        const salesOrderPrice = parseFloat(salesOrderPriceValue.value);

        // Compare salesOrderPrice with totalAmount
        if (Math.abs(totalAmount - salesOrderPrice) >= 1) {
          setPriceError("The difference between the price is more than 1");
          return;
        }
      }

      const fileFormData = new FormData();
      fileFormData.append("form_data", JSON.stringify(form_Data));
      fileFormData.append("file", getFile[0]);
      fileFormData.append("shipment_id", shipmentData._id as string);
      fileFormData.append(
        "action_link_id",
        formData.get("action_link_id") as string
      );
      const options = {
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      };
      axios
        .post(url, fileFormData, options)
        .then((response) => {
          console.log("File upload response:", response.data);
        })
        .catch((error) => {
          console.error("File upload error:", error);
          setError(error.response?.data || "File upload failed");
        });
      setOpen(false);
      setPriceError(null);
      return;
    }

    const myHeaders = new Headers();
    !hasFile && myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${Cookies.get("token")}`);

    const raw = JSON.stringify(value);

    const modifiedFormData = new FormData();
    let endpoint = `${config.BACKEND_URL}${formData.get("actionRoute")}`;

    if (hasFile) {
      modifiedFormData.append("file", getFile[0]);

      const actionRoute = formData.get("actionRoute");

      let routePath = "";

      if (
        typeof actionRoute === "string" &&
        actionRoute.includes(":shipmentId")
      ) {
        routePath = actionRoute.replace(":shipmentId", shipmentData._id);
      }

      // endpoint = `${config.BACKEND_URL}/shipment/${shipmentData._id}/edit?sla=${differenceInDays}&action_link_id=${formData.get('action_link_id')}&shipment_id=${shipmentData._id}`;
      endpoint = `${config.BACKEND_URL}/${routePath}?sla=${
        matchedValues[0]
      }&action_link_id=${formData.get("action_link_id")}&shipment_id=${
        shipmentData._id
      }`;
    } else if (formData.get("action_unique_key") == "cancel_shipment") {
      const actionRoute = formData.get("actionRoute");

      let routePath = "";

      if (
        typeof actionRoute === "string" &&
        actionRoute.includes(":shipmentId")
      ) {
        routePath = actionRoute.replace(":shipmentId", shipmentData._id);
      }

      endpoint = `${config.BACKEND_URL}/${routePath}`;
    } else if (
      formData.get("action_unique_key") == "auto_inventory_allocation"
    ) {
      endpoint = `${config.BACKEND_URL}/shipment/autoAllocate`;
    }

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: hasFile ? modifiedFormData : raw,
    };

    fetch(`${endpoint}`, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        if (result?.responseCode === 1 && result?.errors?.length > 0) {
          setError(result.errors[0].message);
        } else if (
          result?.data?.failedShipments &&
          result.data.failedShipments.length > 0
        ) {
          setError(result.data.failedShipments[0].error);
        }
        setOpen(false);
      })
      .catch((error) => {
        setError("Something went wrong.");
        setOpen(false);
      });
  }

  const [hasFormData, setHasFormData] = useState(true);
  useEffect(() => {
    const field = data?.find((field) => field._id === formId);
    if (field) {
      setHasFormData(field.formData.length > 0);
    }
  }, [data, formId]);

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog className="relative z-10" onClose={setOpen}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <form onSubmit={updateShipment}>
                  <div className="space-y-12">
                    <div className="border-b border-gray-900/10 pb-12">
                      {/* <h2 className="text-base font-semibold leading-7 text-gray-900">Actions</h2> */}
                      <div className="mt-10">
                        {data?.map((field: any, index: number) => {
                          return (
                            <>
                              {field._id == formId ? (
                                <>
                                  <input
                                    type="hidden"
                                    value={field._id}
                                    name="action_link_id"
                                  />
                                  <input
                                    type="hidden"
                                    value={field.actionRoute}
                                    name="actionRoute"
                                  />
                                  <input
                                    type="hidden"
                                    value={field.action_unique_key}
                                    name="action_unique_key"
                                  />
                                  <>
                                    {field.formData.length > 0 ? (
                                      <>
                                        {field.formData.map((value, index) => {
                                          return (
                                            <div
                                              key={index}
                                              className="sm:col-span-12 pt-4"
                                            >
                                              <input
                                                type="hidden"
                                                value={value._id}
                                                name="input_id"
                                              />
                                              <label
                                                htmlFor={value.input_name.toLowerCase()}
                                                className="block text-sm font-medium leading-6 text-gray-900"
                                              >
                                                {value.input_name}
                                              </label>
                                              <div className="mt-2">
                                                {value.input_box ==
                                                "boolean" ? (
                                                  <input
                                                    type="checkbox"
                                                    name={value.input_name.toLowerCase()}
                                                    id={value.input_name.toLowerCase()}
                                                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                  />
                                                ) : (
                                                  <>
                                                    <input
                                                      required={
                                                        value.is_mandatory
                                                          ? value.is_mandatory
                                                          : false
                                                      }
                                                      type={value.input_box}
                                                      name={value._id}
                                                      id={value.input_name.toLowerCase()}
                                                      className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                    />
                                                  </>
                                                )}
                                              </div>
                                            </div>
                                          );
                                        })}
                                        {priceError && (
                                          <p className="text-red-600 text-right font-bold">
                                            {priceError}
                                          </p>
                                        )}
                                      </>
                                    ) : field.action_unique_key ===
                                      "auto_inventory_allocation" ? (
                                      <>
                                        <h2 className="text-lg text-center font-semibold leading-7 text-gray-900">
                                          Confirm Auto-Inventory Allocation
                                        </h2>
                                        <p className="text-sm pt-4 text-center text-[#222222]">
                                          Are you sure you want to proceed with
                                          auto-allocating inventory?
                                        </p>
                                      </>
                                    ) : (
                                      <>
                                        <h2 className="text-lg text-center font-semibold leading-7 text-gray-900">
                                          Download File
                                        </h2>
                                        <p className="text-sm pt-4 text-center text-[#222222]">
                                          Click the button below to download
                                          your file.
                                        </p>

                                        {field.action_unique_key ==
                                        "generate_pi" ? (
                                          <div className="pt-4">
                                            <label>
                                              <input
                                                className="mr-2"
                                                onChange={handleCheckboxChange}
                                                type="checkbox"
                                                name="fileType"
                                                value={"sheet"}
                                              />
                                              Sheet (xlsx) format
                                            </label>
                                            <br />
                                            <label>
                                              <input
                                                className="mr-2"
                                                onChange={handleCheckboxChange}
                                                type="checkbox"
                                                name="fileType"
                                                value={"pdf"}
                                              />
                                              PDF
                                            </label>
                                          </div>
                                        ) : (
                                          ""
                                        )}
                                      </>
                                    )}
                                  </>
                                </>
                              ) : (
                                ""
                              )}
                            </>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 flex items-center justify-center gap-x-6">
                    <button
                      type="button"
                      className="text-sm font-semibold leading-6 border-[#1D1C21] text-[#1D1C21] border-2 rounded-md px-3 py-1"
                      onClick={() => {
                        setPriceError(null);
                        setOpen(false);
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                    >
                      {hasFormData ? "Save" : "Confirm"}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
