import type { <PERSON>ada<PERSON> } from "next";
import { Nunito_Sans } from "next/font/google";

const NunitoSans = Nunito_Sans({
  weight: "500",
  subsets: ["latin"],
});

import "./globals.css";

export const metadata: Metadata = {
  title: "Sunrise",
  description: "Sunrise",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={NunitoSans.className}>
        {children}
      </body>
    </html>
  );
}
