import { NotFound } from "../components/notFound";

export function TableRowsWithData(props) {
  const { tableData, selectedOrders, handleCheckbox, router } = props;
  if (tableData.length === 0) {
    return <NotFound />;
  }
  return (
    <>
      {tableData.length > 0 &&
        tableData.map((x: any, i: any) => (
          <tr
            key={i}
            className="even:bg-gray-50 cursor-pointer hover:bg-gray-200"
            onClick={() => router.push(`/orders/detail/${x.orderID}`)}
          >
            <td className="sm:pl-3" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                disabled={
                  x?.status?.toUpperCase() !== "CREATED" &&
                  x?.status?.toUpperCase() !== "PENDING"
                }
                className={`${
                  x?.status?.toUpperCase() !== "CREATED" &&
                  x?.status?.toUpperCase() !== "PENDING"
                    ? "bg-slate-400"
                    : ""
                }`}
                checked={selectedOrders.includes(x.orderID)}
                onChange={(e) => handleCheckbox(e, x.orderID)}
              />
            </td>
            <td className="whitespace-wrap  text-sm font-medium text-gray-900 w-[150px] sm:pl-3  ">
              {x.shopifyOrderNumber}
            </td>
            <td className="whitespace-wrap text-sm font-medium text-gray-900  w-[150px] sm:pl-3  ">
              {x.omsOrderNumber}
            </td>
            <td className="whitespace-wrap text-sm font-medium text-gray-900 sm:pl-3">
              {x.currency} {x.amount}
            </td>
            <td className="whitespace-wrap  text-sm font-medium text-gray-900 w-[150px] sm:pl-3  ">
              {x.budgetCategory}
            </td>
            <td className="whitespace-wrap  text-sm font-medium text-gray-900 w-[150px] sm:pl-3  ">
              {x.preOrderNumber}
            </td>
            <td className="whitespace-wrap  text-sm font-medium text-gray-900 w-[150px] sm:pl-3  ">
              {x.totalLineItems}
            </td>
            <td className="whitespace-wrap  text-sm font-medium text-gray-900 w-[150px] sm:pl-3  ">
              {x?.isFuture.toUpperCase()}
            </td>
            <td className="text-sm font-medium text-gray-900 w-[150px] px-3 py-4 text-center">
              {x.SageOrderNumber}
            </td>
            <td className="text-sm font-medium text-gray-900 w-[150px] px-3 py-4 text-center">
              <span
                className={`inline-block px-2 py-1 rounded text-xs font-semibold break-words max-w-full ${
                  x.status === "created"
                    ? "bg-gray-400 text-[#222222]"
                    : x.status === "pending"
                    ? "bg-blue-100 text-blue-800"
                    : x.status === "rejected"
                    ? "bg-red-100 text-red-800"
                    : x.status === "order placed "
                    ? "bg-sky-100 text-[#222222]"
                    : x.status === "invoiced"
                    ? "bg-purple-100 text-purple-800"
                    : x.status === "on hold"
                    ? " bg-orange-100 , text-[#222222]"
                    : x.status === "out for delivery"
                    ? "bg-[#D6FBF2] , text-[#222222]"
                    : x.status == "order completed"
                    ? "bg-green-100 text-green-800"
                    : x.status === "order deleted"
                    ? "bg-[#F4A4AB] , text-white"
                    : "bg-purple-100 text-purple-800"
                }`}
              >
                {x.status.toUpperCase()}
              </span>
            </td>
            {/* <td className="text-sm font-medium text-gray-900 w-[150px] px-3 py-4 text-center">
              <span className="inline-block px-2 py-1 rounded text-xs font-semibold break-words max-w-full bg-purple-100 text-purple-800">
                {x.ecpacStatus?.toUpperCase()}
              </span>
            </td> */}
          </tr>
        ))}
    </>
  );
}
