"use client";

import React, { useState, useEffect, Suspense } from "react";
import Image from "next/image";
import SunriseBlack from "../assets/Sunrise_TradeWhite.png";
import PasswordReset from "../components/PasswordReset";
import ResetPasswordForm from "../components/ResetPasswordForm";
import { useSearchParams, useRouter } from "next/navigation";
import config from "../../../config.json";
import SunriseBanner from "../assets/Sunrise_trade_banner.jpg";

const ResetPasswordContent = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const hasToken = searchParams.get("token");

  useEffect(() => {
    const verifyToken = async () => {
      if (hasToken) {
        try {
          const response = await fetch(
            `${config.BACKEND_URL}/user/verify-reset-token/${hasToken}`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

            const data = await response.json();
            
          if (data.responseCode === 0 && data.status === "success") {
            setIsValidToken(true);
          } else {
            setIsValidToken(false);
            setErrorMessage(
              data.errors?.[0]?.message || "Token is invalid or has expired"
            );
          }
        } catch (error) {
          setIsValidToken(false);
          setErrorMessage("An error occurred while verifying the token");
        }
      }
    };

    if (hasToken) {
      verifyToken();
    }
  }, [hasToken]);

  const renderContent = () => {
    if (hasToken) {
      if (isValidToken === null) {
        return (
          <div className="text-center">
            <p>Verifying token...</p>
          </div>
        );
      }

      if (isValidToken === false) {
        return (
          <div className="text-center">
            <p className="text-red-600">{errorMessage}</p>
            <button
              onClick={() => router.push("/login")}
              className="mt-4 px-4 py-2 text-sm font-semibold text-white rounded-md bg-[#222222]"
            >
              Back to Login
            </button>
          </div>
        );
      }

      return <ResetPasswordForm />;
    }

    return <PasswordReset />;
  };

  return (
    <div className="relative min-h-screen w-full overflow-hidden">
      {/* Fullscreen Background Image */}
      <Image
        src={SunriseBanner.src}
        alt="Sunrise Banner"
        fill
        priority
        className="object-cover w-full h-full absolute top-0 left-0 z-0"
      />
      {/* Overlay: Reset Password Modal */}
      <div className="relative z-10 flex items-center min-h-screen">
        <div className="bg-black/50 backdrop-blur-md rounded-lg shadow-lg p-12 ml-16 max-w-lg w-full flex flex-col items-start">
          <div className="logo pb-8">
            <Image
              src={SunriseBlack.src}
              alt="Sunrise"
              width={250}
              height={350}
            />
          </div>
          <p className="text-3xl text-white">
            {hasToken
              ? isValidToken
                ? "Create New Password"
                : "Invalid Token"
              : "Reset your password"}
          </p>
          <div className="w-full pt-8 max-w-md">{renderContent()}</div>
        </div>
      </div>
    </div>
  );
};

const ResetPassword: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gray-900"></div>
      </div>
    }>
      <ResetPasswordContent />
    </Suspense>
  );
};

export default ResetPassword;
