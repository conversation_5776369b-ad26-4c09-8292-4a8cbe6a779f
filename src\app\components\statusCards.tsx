export function StatusCards({ status }) {
  return (
    <>
      {status.map((stats, index) => {
        // Determine if first or last
        const isFirst = index === 0;
        const isLast = index === status.length - 1;

        const roundedClasses = `${
          isFirst ? "rounded-l-2xl" : ""
        } ${isLast ? "rounded-r-2xl" : ""}`;

        return (
          <div
            className={`flex flex-col bg-white shadow p-4 ${roundedClasses}`}
            key={`${stats.type}_index`}
          >
            <dt className="text-sm font-semibold leading-6 text-gray-900">
              {stats.type}
            </dt>
            <dd className="order-first text-3xl font-semibold tracking-tight text-gray-900">
              {stats.count}
            </dd>
          </div>
        );
      })}
    </>
  );
}
