import { useState } from "react";
import Modal from "./modal";
import { useQuery } from '@tanstack/react-query';
import Pagination from './pagination';
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Table({tableHeader, isEdit, isPagination, tableData, page, openModalOnClick, open, setapiData, formValues, nextTab}) {
  const [editData, setEditData] = useState(null);
  const [openEditModal, setOpenEditModal] = useState(false);

  async function fetchData(endpoint: String){
    const response = await fetch(`https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/${endpoint}`);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
      return response.json();
  }

  const { data, isLoading, error, isError, refetch } = useQuery({
      queryKey: ['users'],
      queryFn: () => fetchData(page),
  });

  console.log(page, " pagepage")
  console.log(data, " iiiiiiiiiiii dataaaaaaaaaa")

  const isCamelCase = (str) => {
    const s =
      str &&
      str
        .match(
          /[A-Z]{2,}(?=[A-Z][a-z]+[0-9]*|\b)|[A-Z]?[a-z]+[0-9]*|[A-Z]|[0-9]+/g
        )
        .map((x) => x.slice(0, 1).toUpperCase() + x.slice(1).toLowerCase())
        .join("");

    
    return s.slice(0, 1).toLowerCase() + s.slice(1);
  };


  const toCamelCase = (input) =>
    input
      .split(' ') 
      .map((word, index) =>
        index === 0 
          ? word.toLowerCase()
          : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
      )
      .join('');

  const res = data?.data?.data?.length > 0 && data?.data?.data;

  let finalData: any[] = tableData || [];
  const filterObj = (obj, ...allowedFields) => {
    const requiredFields = allowedFields.map(field => toCamelCase(field));
    const newObj = {};
    Object.keys(obj).forEach((element) => {
        if (requiredFields.includes(element)) newObj[element] = obj[element];
    });
    return newObj;
  }

  if (data?.data?.data?.length > 0 && page != "order") {
    data.data.data.forEach((res: String) => {
      let filteredData = {};
      tableHeader.forEach((key: String) => {
        let newKey = key.replaceAll(" ", "");
        newKey = isCamelCase(newKey);
        if (res.hasOwnProperty(newKey)) {
          if(newKey == "createdAt" || newKey == "updatedAt"){
            const date = new Date(res[newKey]);
                const formattedDate = date.toLocaleDateString('en-GB', {
                  day: '2-digit', 
                  month: '2-digit', 
                  year: '2-digit' 
                });

                filteredData[newKey] = formattedDate;
                filteredData["id"] = res["_id"];
          }else if(newKey == "to"){
            filteredData[newKey] = res[""];
            filteredData["id"] = res["_id"];
          }else{
            filteredData[newKey] = res[newKey];
            filteredData["id"] = res["_id"];
          }
        }
      });
      finalData.push(filteredData);
    });

    
    
  };
  
  if (page == "orderDetail") {
    console.log("inside order detail if")
    finalData = tableData?.map((lineItemData) => filterObj(lineItemData, "SKU", "Category", "Status", "Price", "Quantity", "ID", "Amount"));
  }
  

  if(page == "order"){
    data?.data?.data.forEach((res) => {
      let filteredData = {};
      tableHeader.forEach((key) => {
          let newKey = toCamelCase(key);
          
          // Mapping the newKey to specific data points in the object
          switch(newKey) {
              case "id":
                  filteredData[newKey] = res?._id;
                  break;
              case "name":
                  filteredData[newKey] = res?.name;
                  break;
              case "customer":
                  filteredData[newKey] = res?.customer?.first_name + " " + res?.customer?.last_name;
                  break;
              case "status":
                  filteredData[newKey] = res?.status; 
                  break;
              case "paymentStatus":
                  filteredData[newKey] = res?.financial_status; 
                  break;
              case "country":
                  filteredData[newKey] = res?.shipping_address?.country;
                  break;
              case "date":
                const date = new Date(res?.createdAt);
                const formattedDate = date.toLocaleDateString('en-GB', {
                  day: '2-digit', 
                  month: '2-digit', 
                  year: '2-digit' 
                });
                  filteredData[newKey] = formattedDate;
                  break;
              case "amount":
                  filteredData[newKey] = res?.line_items[0]?.price;
                  break;
              default:
                  if (res.hasOwnProperty(newKey)) {
                      filteredData[newKey] = res[newKey];
                  }
                  break;
          }
          console.log(res, "filtered res");
      });
      filteredData["id"] = res["_id"];
      finalData.push(filteredData);
  });
  }
  

  console.log(finalData, " final dataaaaaaaaaaaaaaa")

  if(!isLoading) setapiData(data);

  console.log(isLoading, " isLoading")

  function editModal(filterValue: Number){
    console.log(filterValue, " filter value ", data)
    const newData = data?.data?.data?.filter((val: Object, index: Number) => {if(index == filterValue) return val});
    console.log(newData, " new data")
    setEditData(newData);
    setOpenEditModal(!openEditModal);
  }

  const router = usePathname();

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        {/* <div className="sm:flex-auto">
          <h1 className="text-base font-semibold leading-6 text-gray-900">Users</h1>
          <p className="mt-2 text-sm text-gray-700">
            A list of all the users in your account including their name, title, email and role.
          </p>
        </div>
        <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <button
            onClick={() => {openModalOnClick("add")}}
            type="button"
            className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            Add user
          </button>
        </div> */}
      </div>
      <div className="mt-8 flow-root">
        <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    {tableHeader?.map((item: String, index) => {
                      return (
                        <th key={index} scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          {item}
                        </th>
                      )
                    })}
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">Edit</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                {/* {finalData?.map((rowData: Object, index) => (
                    <tr key={index}>
                        {Object.entries(rowData)
                        // .filter(([key]) => key !== "id")
                        .map(([key, value]) => (
                          <td key={key} className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                                <Link href={`/orders/${nextTab}/${key == "id" ? value : ""}`}>
                                  {value}
                                </Link> 
                              </td>
                        ))}
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                          <span onClick={() => editModal(index)} className="text-indigo-600 cursor-pointer hover:text-indigo-900">
                            Edit
                          </span>
                        </td>
                      </tr>
                  ))} */}

                  {finalData?.map((rowData: any, index) => (
                    <tr key={index}>
                      {Object.entries(rowData)                     
                        .filter((it) => (page === "order") ? (it[0] !== "id" && it[1] !== "") : (it[0] !== "id" || it[1] !== ""))                     
                        // .filter((it) => it[0] !== "id" || it[1] != "" || it[0] != undefined || it[0] != null || it[1] != undefined || it[1] != null)
                        .map((item: any) => (
                          <td key={item[0]} className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                           {console.log(rowData, " rowData")}
                           {router.includes("/orders") || router.includes("/orders/detail") || router.includes("/orders/shipment") || router.includes("/orders/attributes") || router.includes("/orders/transaction") || router.includes("/orders/comment") ? 
                                <Link href={`/orders/${nextTab}/${rowData?.id}`}>
                                    {item[1] == null ? " " : item[1]}
                                </Link> 
                            :
                            (router.includes("/shipments") || router.includes("/shipments/details") || router.includes("/shipments/attributes") || router.includes("/shipments/comment") ? 
                                  <Link href={`/shipments/${nextTab}/${rowData?.id}`}>
                                    {item[1] == null ? " " : item[1]}
                                  </Link> 
                                :
                                    item[1] == null ? " " : item[1]
                              )
                            }

                          </td>
                        ))}

                        {isEdit && 
                          <td className="relative whitespace-nowrap text-right text-sm font-medium sm:pr-0">
                            <span onClick={() => editModal(index)} className="text-indigo-600 cursor-pointer hover:text-indigo-900">
                              Edit
                            </span>
                          </td>
                        }
                    </tr>
                  ))}


                </tbody>

                {/* <tbody className="divide-y divide-gray-200">
                  {data?.data?.data.map((data, index) => (
                    <tr key={data.email}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                        {data.name}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{data.shopifyCompanyId}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{data.email}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{data.priority}</td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{data.internalCode}</td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                        <span onClick={() => editModal(index)} className="text-indigo-600 cursor-pointer hover:text-indigo-900">
                          Edit<span className="sr-only">, {data.name}</span>
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody> */}
              </table>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-12">
        {/* {isPagination && 
          <Pagination />
        } */}
      </div>
      {openEditModal && <Modal fields={""} open={openEditModal} formValues={formValues} setOpen={setOpenEditModal} page={page} data={editData} modalType={"edit"} openModalOnClick={editModal} refetch={refetch} />}
    </div>
  )
}
