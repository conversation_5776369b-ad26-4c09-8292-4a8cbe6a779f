"use client";
import React, {
  useEffect,
  useState,
  Fragment,
  useCallback,
  useMemo,
} from "react";
import Sidebar from "../components/sidebar";
import Table from "../components/table";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import Tabs from "../components/tabs";
import axios from "axios";
import config from "../../../config.json";
import currency from "../../../Currency.json";
import Loader from "../components/Loader";
import {
  Dialog,
  Disclosure,
  Menu,
  Popover,
  Transition,
} from "@headlessui/react";
import {
  XMarkIcon,
  MagnifyingGlassIcon,
  UserIcon,
  XCircleIcon,
} from "@heroicons/react/24/outline";
import {
  ChevronDownIcon,
  ArrowPathIcon,
  MagnifyingGlassCircleIcon,
} from "@heroicons/react/20/solid";
import Pagination from "../components/pagination";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import SuccessNotification from "../components/SuccessNotifications";
import ErrorNotification from "../components/ErrorNotification";
import { ImSpinner8 } from "react-icons/im";
import { useDebounce } from "@/hooks/useDebounce";

const queryClient = new QueryClient();
export default function Customers() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [tableData, setTableData]: any = useState([]);
  const [file, setFile] = useState(null);
  const [status, setStatus] = useState("");
  const [salesperson, setSalesPerson]: any = useState("");
  const [salesPersonsList, setSalesPersonsList]: any = useState([]);
  const [paginationCount, setPaginationCount]: any = useState(0);
  const [loader, setLoader]: any = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const limit = 50;
  const [paginationInfo, setPaginationInfo]: any = useState({
    currentPage: 1,
    totalPages: 1, // Update to reflect total number of pages
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [numberLoading, setNumberLoading] = useState(false);
  const [customerNumber, setCustomerNumber] = useState({});
  const [loading, setLoading] = useState(false);
  const [successNotification, setSuccessNotification] = useState(false);
  const [errorNotification, setErrorNotification] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const debouncedValue = useDebounce(searchTerm, 500);

  const customers = [{ shopifyCustomerId: "7109687017572", status: "active" }];
  // let userRole: any;
  const [userRole, setUserRole]: any = useState("");
  // Fetch customers list with filters and pagination
  async function fetchCustomersList(page: number, filters: any = {}) {
    const myHeaders = {
      Authorization: `Bearer ${Cookies.get("token")}`,
      "Content-Type": "application/json",
    };

    setLoader(true);

    try {
      const params: any = { page, limit };

      if (filters.status) {
        params.status = filters.status;
      }
      if (filters.salesperson) {
        params.salespersonId = filters.salesperson;
      }

      const isFiltered =
        filters.searchTerm || filters.status || filters.salesperson;
      const endpoint = isFiltered
        ? `${config.BACKEND_URL}/distributor/filterDistributors`
        // : `${config.BACKEND_URL}/distributor`;
        : `${config.BACKEND_URL}/distributor`;

      if (filters.searchTerm) {
        params.email = filters.searchTerm;
        params.name = filters.searchTerm;
        params.companyName = filters.searchTerm;
        params.shopifyCustomerId = filters.searchTerm;
      }
      const response = await axios.get(endpoint, {
        params, // Include your query parameters here
        headers: myHeaders, // Add headers here
      });

      const data = response.data.data.data.map((x: any) => ({
        id: x.shopifyCustomerId || "N/A",
        name: x.name,
        companyName: x.companyName,
        email: x.email,
        salespersonName:x.salespersonName,
        status: x.status,
        createdAt: new Date(x.createdAt).toString().slice(0, 15),
        phone: x.phone,
        brn: x.companyDetails?.brn || "",
      }));
      setUserRole(Cookies.get("userrole"));
      // console.log("User Role:", Cookies.get("userrole"));
      setTableData(data);
      const totalCount = Number(response.data.result);
      const totalPages = Math.ceil(totalCount / limit);
      setPaginationCount(totalPages);
    } catch (error) {
      console.error("Error fetching customer data", error);
    } finally {
      setLoader(false);
      setIsSearching(false);
    }
  }

  //fetcg customer number by the status
  async function fetchCustomerNumber() {
    setNumberLoading(true);
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/distributor/getDistributorStatusCount`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );
      setCustomerNumber(response.data.data.formattedCounts);
    } catch (error) {
      console.error("Error fetching salespersons", error);
      setNumberLoading(false);
    } finally {
      setNumberLoading(false);
    }
  }
  async function fetchSalespersonsList() {
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/department_people`
      );
      const allPeople = response.data.data.data;

      const salespeople = allPeople.filter((person) =>
        person.departmentType.some(
          (department) => department.department === "Sales Person"
        )
      );
      setSalesPersonsList(salespeople);
    } catch (error) {
      console.error("Error fetching salespersons", error);
    }
  }
  const handlePageChange = (newPage: number) => {
    fetchCustomersList(newPage, {
      status, // Pass the currently applied filters
      salesperson,
      searchTerm,
    });
  };

  const getData = async () => {
    await Promise.all([
      fetchCustomersList(1, { status, salesperson }),
      fetchSalespersonsList(),
      fetchCustomerNumber(),
    ]);

    setTimeout(() => {
      setRefreshButtonClicked(false);
    }, 500);
  };
  useEffect(() => {
    getData();
  }, [status, salesperson]);

  useEffect(() => {
    if (debouncedValue) {
      fetchCustomersList(1, { status, salesperson, searchTerm });
    }
  }, [debouncedValue]);

  // useEffect(()=>{
  //   fetchSalespersonsList();
  // },[])
  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!event.target.value) {
        getData();
        setIsSearching(false);
        setSearchTerm("");
        return;
      }
      setIsSearching(true);
      setSearchTerm(event.target.value);
    },
    []
  );

  const handleSalespersonChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSalesPerson(event.target.value);
  };

  const handleStatusChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setStatus(event.target.value);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      const fileExtension = selectedFile.name.split(".").pop().toLowerCase();
      if (fileExtension !== "xlsx" && fileExtension !== "xls") {
        setErrorMessage("Only Excel files are allowed!");
        setFile(null); // Clear the file if invalid
      } else {
        setErrorMessage(""); // Clear any previous error message
        setFile(selectedFile); // Save the valid file to state
      }
    }
  };

  const handleFileUpload = async () => {
    if (!file) {
      setErrorMessage("Please upload a valid Excel file");
      return;
    }
    setLoading(true);

    const formData = new FormData();
    formData.append("sheet", file);

    try {
      const response = await axios.post(
        `${config.BACKEND_URL}/distributor/uploadDistributorSheet`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        }
      );

      if (response.data.status === "success") {
        const result = response.data.data.distributors;
        // setSuccessMessage(result.message);
        setOpen(false);
        setFile(null);
        setLoading(false);
        setSuccessNotification(true);
        setErrorMessage(
          response.data.data.message ||
            "Distributor sheet processed successfully"
        );
        setTimeout(() => {
          setSuccessNotification(false);
          setErrorMessage("");
        }, 5000);
      } else {
        setErrorMessage("Something went wrong");
        console.error("Error uploading file:", response.statusText);
        setLoading(false);
      }
    } catch (error: any) {
      if (error?.response?.data?.error?.status === "fail") {
        setTimeout(() => {
          if (error?.response?.data?.error?.data?.errorSheet?.buffer) {
            const fileData =
              error.response.data.error.data.errorSheet.buffer.data;

            // Create a Blob directly from Uint8Array
            const blob = new Blob([new Uint8Array(fileData)], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            });

            // Create a download link
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = "InvalidRows.xlsx"; // Set the file name
            document.body.appendChild(link);
            link.click(); // Trigger the download
            document.body.removeChild(link); // Clean up
            URL.revokeObjectURL(link.href); // Free memory
          }
        }, 5000);
        setErrorMessage(error?.response?.data?.message);
        setLoading(false);
        setOpen(false);
        setFile(null);
        setErrorNotification(true);
        setTimeout(() => {
          setErrorNotification(false);
          setErrorMessage("");
        }, 5000);
      }
      setLoading(false);
      console.log("Error:", error);
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        {errorNotification && <ErrorNotification message={errorMessage} />}
        {successNotification && <SuccessNotification message={errorMessage} />}
        <div className="flex p-4 sm:px-6 lg:px-8 items-center">
          <h2 className="text-3xl w-[95%] font-semibold leading-6 text-gray-900">
            Customers
          </h2>
          <button
            type="button"
            className="flex justify-around w-[5%] rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
            onClick={() => {
              setRefreshButtonClicked(true);
              getData();
            }}
          >
            <ArrowPathIcon
              className={`${
                refreshButtonClicked ? "animate-spin" : "animate-none"
              } h-5 w-10`}
            />
          </button>
        </div>
        <div>
          <div className="p-4 sm:px-6 lg:px-8">
            <div className="flow-root">
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <div className="flex overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white">
                    <div className="flex min-w-full divide-x divide-gray-300">
                      <div className="p-6 w-1/3 text-center">
                        <div className="text-2xl font-bold text-black">
                          {customerNumber["New Customer"] || 0}
                        </div>
                        <div className="text-black">New Customer Applied</div>
                      </div>
                      <div className="p-6 w-1/3 text-center">
                        <div className="text-2xl font-bold text-black">
                          {customerNumber["Approved: Level 1"] || 0}
                        </div>
                        <div className="text-black">Salesperson Assigned</div>
                      </div>
                      {/* <div className="p-6 w-1/3 text-center">
                        <div className="text-2xl font-bold text-black">
                          {customerNumber["Pending Salesperson Allocation"] ||
                            0}
                        </div>
                        <div className="text-black">
                          Pending Salesperson Allocation
                        </div>
                      </div> */}
                      <div className="p-6 w-1/3 text-center">
                        <div className="text-2xl font-bold text-black">
                          {customerNumber["Customer Created in Sage"] || 0}
                        </div>
                        <div className="text-black">
                          Customer Created in Sage
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end p-4 sm:px-6 lg:px-8">
            {(userRole === "Sunrise Admin" ||
              userRole === "Finance Manager" ||
              userRole === "FINANCE EXECUTIVE") && (
              <button
                onClick={() => setIsModalOpen(true)}
                type="button"
                className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Update SalesPerson in Bulk
              </button>
            )}

            {isModalOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                onClick={() => setIsModalOpen(false)}
              >
                <div
                  className="bg-white p-6 rounded-lg max-w-sm w-full aspect-video"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex justify-end">
                    <button
                      onClick={() => setIsModalOpen(false)}
                      className="text-[#222222] hover:text-gray-700"
                    >
                      ✕
                    </button>
                  </div>
                  <div className="flex flex-col items-center justify-between h-full">
                    <div className="text-gray-800 text-justify">
                      Download Excel Sheet in which you will get the list of
                      customers with assigned sales person and upload sheet to
                      update the sales person to the customer.
                    </div>
                    <div>
                      {(userRole === "Sunrise Admin" ||
                        userRole === "Finance Manager" ||
                        userRole === "FINANCE EXECUTIVE") && (
                        <div className="flex justify-end gap-4">
                          <button
                            onClick={async () => {
                              try {
                                const response = await fetch(
                                  `${config.BACKEND_URL}/distributor/downloadDistributorSheet`,
                                  {
                                    method: "GET",
                                    headers: {
                                      Authorization: `Bearer ${Cookies.get(
                                        "token"
                                      )}`,
                                      Accept:
                                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    },
                                  }
                                );

                                if (!response.ok) {
                                  throw new Error("Failed to download file");
                                }

                                const blob = await response.blob();
                                const url = window.URL.createObjectURL(blob);
                                const link = document.createElement("a");
                                link.href = url;
                                link.download = "SalesPerson Update Sheet.xlsx";
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                                window.URL.revokeObjectURL(url);

                                setIsModalOpen(false);
                              } catch (error) {
                                console.error("Error downloading file:", error);
                              }
                            }}
                            type="button"
                            className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                          >
                            Download Excel
                          </button>
                          <button
                            onClick={() => {
                              setOpen(true);
                              setIsModalOpen(false);
                            }}
                            type="button"
                            className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                          >
                            Upload Sheet
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="p-4 sm:px-6 lg:px-8">
            <div className="flow-root bg-green-50">
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <div className="flex overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white">
                    <div className="p-4 flex min-w-full divide-y divide-gray-300 space-x-8">
                      <div className="relative flex-grow">
                        <input
                          type="text"
                          placeholder="Search"
                          value={searchTerm}
                          onChange={handleSearchChange}
                          className="relative w-full border rounded-lg text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 pl-10"
                        />
                        {isSearching && (
                          <span className="absolute top-1/2 transform -translate-y-1/2 right-10">
                            <ImSpinner8
                              fill="black"
                              size={20}
                              className={`${
                                isSearching ? "animate-spin" : "animate-none"
                              }`}
                            />
                          </span>
                        )}
                        <MagnifyingGlassIcon
                          className="h-6 w-6 text-black absolute left-3 top-1/2 transform -translate-y-1/2"
                          aria-hidden="true"
                        />
                        {searchTerm && (
                          <XCircleIcon
                            onClick={() => {
                              setSearchTerm("");
                              getData();
                            }}
                            className="h-6 w-6 text-black absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                            aria-hidden="true"
                          />
                        )}
                      </div>

                      {userRole !== "Sales Person" && (
                        <div>
                          <select
                            value={salesperson}
                            onChange={handleSalespersonChange}
                            className="px-8 py-2 rounded-lg text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          >
                            <option value="">Select a salesperson</option>

                            {salesPersonsList.map((person) => (
                              <option key={person._id} value={person._id}>
                                {person.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      )}

                      <div>
                        <select
                          value={status}
                          onChange={handleStatusChange}
                          className="px-8 py-2 border rounded-lg text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        >
                          <option value="">Select Status</option>
                          <option value="New Customer">New Customer</option>
                          <option value="Approved: Level 1">
                            Approved: Level 1
                          </option>
                          {/* <option value="Pending Salesperson Allocation">
                            Pending Salesperson Allocation
                          </option> */}
                          <option value="Customer Created in Sage">
                            Customer Created in Sage
                          </option>
                          <option value="Inactive">Inactive</option>
                          <option value="Rejected">Rejected</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="px-4 sm:px-6 lg:px-8 ">
            <div className="mt-4 flow-root">
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                     <div className="overflow-x-auto w-full">
                      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        <table className="min-w-full table-fixed divide-y divide-gray-300">
                      <thead className="bg-[#222222] text-white">
                        <tr>
                          <th
                            scope="col"
                            className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-white sm:pl-6"
                          >
                            Customer ID
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Customer Name
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Company Name
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Phone
                          </th>
                            <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Sales Person
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Date Applied
                          </th>
                          <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Status
                          </th>
                          {/* <th
                            scope="col"
                            className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                          >
                            Update
                          </th> */}
                        </tr>
                      </thead>

                      <tbody className="divide-y divide-gray-200 bg-white">
                        {loader ? (
                          <tr>
                            <td colSpan={7} className="py-6 text-center ">
                              <ImSpinner8
                                fill="black"
                                size={30}
                                className="animate-spin inline-block"
                              />
                            </td>
                          </tr>
                        ) : (
                          <CustomerTable
                            tableData={tableData}
                            router={router}
                          />
                        )}
                      </tbody>
                    </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="px-4 sm:px-6 lg:px-8 ">
            <div className="mt-4 flow-root">
              <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                  <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                    {paginationCount > 1 && (
                      <Pagination
                        paginationCount={paginationCount}
                        onPageChange={handlePageChange}
                      />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Transition.Root show={open} as={Fragment}>
          <Dialog as="div" className="relative z-50" onClose={setOpen}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />
            </Transition.Child>

            <div className="fixed inset-0 z-50 overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <div className="fixed inset-0 z-10 w-screen bg-black bg-opacity-50 overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                      <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-sm sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
                        <div className="flex justify-end">
                          <button
                            onClick={() => setOpen(false)}
                            className="text-[#222222] hover:text-gray-700"
                          >
                            ✕
                          </button>
                        </div>

                        <div>
                          <Dialog.Title
                            as="h3"
                            className="text-base text-gray-900 text-left"
                          >
                            Upload Free Stock Sheet
                          </Dialog.Title>

                          <div className="mt-8">
                            <label
                              className="block mb-2 text-sm font-small text-gray-900"
                              htmlFor="small_size"
                            >
                              Upload the Stock
                            </label>

                            <input
                              className="block w-full mb-5 text-xs text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-[#222222] dark:border-gray-600 dark:placeholder-gray-400"
                              id="small_size"
                              type="file"
                              onChange={handleFileChange}
                            />

                            <p className="text-xs text-red-500">
                              {" "}
                              {errorMessage}
                            </p>
                          </div>
                        </div>

                        <div></div>

                        <div className="mt-5 sm:mt-6 flex justify-between">
                          {/* <div className="mt-7">

                    <p

                      className="text-blue-600 text-xs cursor-pointer underline"

                      // onClick={() => getSampleFile()}

                    >

                      Download Sample File

                    </p>

                  </div> */}

                          <div>
                            <button
                              type="button"
                              onClick={() => {
                                setOpen(false);

                                setErrorMessage("");

                                setFile(null);
                              }}
                              className="inline-flex justify-center rounded-md  px-3 py-2 text-sm font-semibold text-black border shadow-sm hover:bg-[#222222] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 mr-3"
                            >
                              Cancel
                            </button>

                            {loading ? (
                              <button
                                disabled
                                type="button"
                                className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                              >
                                <svg
                                  aria-hidden="true"
                                  role="status"
                                  className="inline mr-3 w-4 h-4 text-white animate-spin"
                                  viewBox="0 0 100 101"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                                    fill="#E5E7EB"
                                  ></path>
                                  <path
                                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                                    fill="currentColor"
                                  ></path>
                                </svg>
                                Loading...
                              </button>
                            ) : (
                              <button
                                type="button"
                                onClick={() => handleFileUpload()}
                                className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                              >
                                Import
                              </button>
                            )}
                          </div>
                        </div>
                      </Dialog.Panel>
                    </div>
                  </div>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>
      </Sidebar>
    </QueryClientProvider>
  );
}

function CustomerTable(props) {
  const { tableData, router } = props;

  if (tableData?.length === 0) {
    return (
      <tr>
        <td
          colSpan={6}
          className="py-4 text-center text-sm font-medium text-[#222222]"
        >
          No data available
        </td>
      </tr>
    );
  }
  return (
    <>
      {tableData.map((person, x) => (
        <tr
          key={person.id}
          className="cursor-pointer hover:bg-gray-100"
          onClick={() => router.push(`/customers/detail/${person.id}`)}
        >
          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
            {person.id}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {person.name}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {person.companyName || "N/A"}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {person.phone}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {person.salespersonName || "-"}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
            {person.createdAt}
          </td>
          <td className="whitespace-nowrap px-3 py-4 text-sm">
            <span
              className={`px-2 inline-flex text-xs leading-5 font-semibold rounded ${
                person.status === "New Customer" &&
                (person.brn?.toLowerCase() === "n/a" || person.brn === "")
                  ? "bg-orange-100 text-orange-800"
                  : person.status === "New Customer"
                  ? "bg-blue-100 text-blue-800"
                  : person.status === "Approved: Level 1"
                  ? "bg-green-100 text-green-800"
                  : person.status === "Pending Salesperson Allocation"
                  ? "bg-yellow-100 text-yellow-800"
                  : person.status === "Customer Created in Sage"
                  ? "bg-purple-100 text-purple-800"
                  : person.status === "Inactive"
                  ? "bg-red-100 text-red-800"
                  : person.status === "Rejected"
                  ? "bg-red-100 text-red-800"
                  : person.status === "Application in progress"
                  ? "bg-orange-100 text-orange-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {/*
                                      Display "Application in progress" when status is "New Customer" and BRN is "N/A",
                                      but keep the actual status as "New Customer" for filtering purposes
                                    */}
              {person.status === "New Customer" &&
              (person.brn?.toLowerCase() === "n/a" || person.brn === "")
                ? "Application in progress"
                : person.status}
            </span>
          </td>
        </tr>
      ))}
    </>
  );
}
