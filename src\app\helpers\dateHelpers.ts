export const formattedDate = (date: any | Date) => {
  const formattedTime = { hour: "", min: "", period: "" };
  const parseDate = typeof date === "string" ? new Date(date) : date;

  if (!(parseDate instanceof Date) || isNaN(parseDate.getTime())) {
    return formattedTime;
  }

  // Use Malaysia timezone
  const options: Intl.DateTimeFormatOptions = {
    timeZone: "Asia/Kuala_Lumpur",
    hour: "numeric",
    minute: "numeric",
    hour12: false,
  };

  const parts = new Intl.DateTimeFormat("en-US", options)
    .formatToParts(parseDate)
    .reduce((acc, part) => {
      if (part.type === "hour") acc.hour = part.value.padStart(2, "0");
      if (part.type === "minute") acc.min = part.value.padStart(2, "0");
      if (part.type === "dayPeriod") acc.period = part.value.toUpperCase();
      return acc;
    }, { ...formattedTime });

  return parts;
};
