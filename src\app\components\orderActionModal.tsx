import axios from "axios";
import Cookies from "js-cookie";
import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useRef, useState } from "react";
import { CheckIcon } from "@heroicons/react/24/outline";
import config from "../../../config.json";

export default function OrderActionModal({
  open,
  setOpen,
  type,
  orderId,
  fetchOrderDataSingle,
  message,
  setMessage,
}) {
  const fileInputRef = useRef(null);
  const [uploadSheet, setUploadSheet] = useState(false);

  async function orderProcess() {
    try {
      let request: any;

      if (type == "autoAllocate") {
        request = {
          method: "post",
          url: `${config.BACKEND_URL}/action/autoAllocate`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          data: [orderId],
        };
      } else if (type == "allocateManually") {
        window.open(
          `${config.BACKEND_URL}/frontend/order/${orderId}/generate_sheet`
        );
      } else if (type == "rejectOrder") {
        request = {
          method: "post",
          url: `${config.BACKEND_URL}/frontend/order/${orderId}/cancel`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
        };
      }

      let response = await axios.request(request);

      if (response.status == 200) {
        if (response.data.data?.failedOrders?.length > 0) {
          const errorMessages = response.data.data.failedOrders.map(
            (order) => `${order.orderName}: ${order.error}`
          );

          setMessage(errorMessages);
        } else {
          setMessage(response.data.data.message);
        }
        setTimeout(() => {
          fetchOrderDataSingle();
          setOpen(false);
        }, 1000);
      }
    } catch (error: any) {
      if (error.response?.data?.errors?.length > 0) {
        setMessage(error.response.data.errors[0].message);
      }
      setOpen(false);

      console.log(error);
    }
  }

  const handleFileUpload = async () => {
    const current: any = fileInputRef.current;
    const formData = new FormData();
    if (current) {
      const file = current.files[0];
      if (!file) {
        alert("Please select a file to upload.");
        return;
      }

      formData.append("file", file);
    }

    try {
      const response = await axios.post(
        `${config.BACKEND_URL}/frontend/order/${orderId}/manualAllocate`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          maxBodyLength: Infinity,
        }
      );

      if (response.status == 200) {
        setMessage(response.data.data.message);
        setTimeout(() => {
          fetchOrderDataSingle();
        }, 1000);
      }
      setUploadSheet(false);
      setOpen(false);
    } catch (error: any) {
      setOpen(false);
      setUploadSheet(false);

      if (error.response?.data?.errors?.length > 0) {
        setMessage(error.response.data.errors[0].message);
      }
    }
  };

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        open={open}
        onClose={() => {
          setOpen(false);
          setUploadSheet(false);
        }}
        className="relative z-10"
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>
        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-lg sm:p-6 data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
              <div>
                {/* <div className="mx-auto flex size-12 items-center justify-center rounded-full bg-green-100">
                <CheckIcon
                  aria-hidden="true"
                  className="size-6 text-green-600"
                />
              </div> */}
                <button
                  type="button"
                  onClick={() => {
                    setOpen(false);
                    setUploadSheet(false);
                  }}
                  className="absolute top-3 right-3 inline-flex items-center justify-center rounded-md bg-gray-100 p-2 text-[#222222] hover:text-gray-700 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <span className="sr-only">Close</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="h-5 w-5"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>

                <div className="mt-3 text-center sm:mt-5">
                  <Dialog.Title
                    as="h3"
                    className="text-base font-semibold text-gray-900"
                  >
                    {type == "autoAllocate"
                      ? "Auto Allocate"
                      : type == "allocateManually"
                      ? "Allocate Manually"
                      : type == "rejectOrder"
                      ? "Reject Order"
                      : ""}
                  </Dialog.Title>
                  <div className="mt-2"></div>
                </div>
              </div>
              <div
                className={`mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense {!uploadSheet && "sm:grid-cols-2"} sm:gap-3`}
              >
                {!uploadSheet && (
                  <button
                    type="button"
                    onClick={() => orderProcess()}
                    className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                  >
                    {type == "allocateManually" ? "Download Sheet" : "Confirm"}
                  </button>
                )}
                {type === "allocateManually" && (
                  <>
                    {!uploadSheet ? (
                      <button
                        type="button"
                        onClick={() => setUploadSheet(true)}
                        className="mt-3 inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-1 sm:mt-0"
                      >
                        Upload Sheet
                      </button>
                    ) : (
                      <div className="mt-3 space-y-4">
                        {/* File Input */}
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="block w-full text-sm text-gray-900 bg-gray-50 rounded-md border border-gray-300 shadow-sm file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-indigo-600 file:text-white hover:file:bg-indigo-500"
                        />

                        {/* Buttons */}
                        <div className="flex justify-end space-x-3">
                          <button
                            type="button"
                            onClick={handleFileUpload}
                            className="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                          >
                            Confirm
                          </button>
                          <button
                            type="button"
                            onClick={() => setUploadSheet(false)}
                            className="inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {type !== "allocateManually" && (
                  <button
                    type="button"
                    data-autofocus
                    onClick={() => setOpen(false)}
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                  >
                    Cancel
                  </button>
                )}
              </div>
            </Dialog.Panel>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
