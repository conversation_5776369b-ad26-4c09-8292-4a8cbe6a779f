"use client";
import React, { useEffect, useState, Fragment, useRef } from "react";
import Sidebar from "../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../config.json";
import Cookies from "js-cookie";
import StatusModal from "../components/statusModal";

const queryClient = new QueryClient();
export default function StatusPage({statusData, departmentData}) {

  interface Department {
    optionName: string;
    optionKey: string;
  }

  const [departmentOptions, setDepartmentOptions] = useState<Department[]>(departmentData || []);
  const scopes = Cookies.get("type");
  const newScopes = scopes && JSON.parse(scopes);

  const [statusTable, setStatusTable] = useState(statusData || []);
  const [open, setOpen] = useState(false);

  async function deleteStatus(id: any) {
    await axios.request({
      method: "delete",
      url: `${config.BACKEND_URL}/status/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    fetchDepartmentData();
    fetchStatusData();
  }



  async function fetchStatusData() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/status`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        id: x._id,
        colorCode: x.colorCode,
        departmentType: x.departmentType?.department,
        statusType: x.statusType,
        statusName: x.status,
        initial: x.isInitialStatus,
      };
    });
    setStatusTable(data);
  }

  async function fetchDepartmentData() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/department_type`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        optionName: x.department,
        optionKey: x._id,
      };
    });
    setDepartmentOptions(data);
  }


  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto"></div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                onClick={() => setOpen(true)}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Create status
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                  <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                    <tr>
                      <th
                        scope="col"
                        className="rounded-tl-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Status
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Status type
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Department
                      </th>
                      <th
                        scope="col"
                        className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Color code
                      </th>
                      {/* <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Action
                      </th> */}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {statusTable.map((x: any) => (
                      <tr key={x.id} className="even:bg-gray-50">
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                          {x.statusName}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.statusType}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.departmentType}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900 ">
                          <div
                            style={{ backgroundColor: `${x.colorCode}` }}
                            className={`p-4 rounded-md w-20`}
                          >
                            {x.colorCode}
                          </div>
                        </td>
                        {/* <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                          {x.initial ? (
                            ""
                          ) : (
                            <button
                              onClick={() => {
                                deleteStatus(x.id);
                              }}
                              type="button"
                              className="block rounded-md bg-red-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
                              Delete
                            </button>
                          )}
                        </td> */}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <StatusModal
            departmentOptions={departmentOptions} 
            setOpen={setOpen}
            open={open} 
            fetchStatusData={fetchStatusData} 
            fetchDepartmentData={fetchDepartmentData}
        />
      </Sidebar>
    </QueryClientProvider>
  );
}
