import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";

export default function StatusDropdown({ handleChange }) {
    const[isStatusChanged, setIsStatusChanged] = useState(false);
    const[statusId, setStatusId] = useState();


  async function fetchData() {
    const response = await fetch(
      `https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/status`
    );
    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return response.json();
  }

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ["users"],
    queryFn: () => fetchData(),
  });

  console.log(data, " shipment dataaaaa");

  const statusDropdownData = data?.data?.data;
  console.log(statusDropdownData, " statusDropdownData");

  const [selectedValue, setSelectedValue] = useState(statusDropdownData[0].status);

  function handleDropdownChange(value) {
    const status = value.split(",")[0];
    const statusId = value.split(",")[1];
    const orderStatus = ""; 
    const orderId = "shipment id"

    setSelectedValue(status)
    handleChange(statusId, status);
  }

  console.log(selectedValue, " selectedValueselectedValueselectedValue")

  return (
    <>
      <label htmlFor="status"> Status </label>
      <select
        id="status"
        value={selectedValue}
        className="block w-full px-2 mb-4 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
        onChange={(e) => handleDropdownChange(e.target.value)}
      >
        {statusDropdownData?.map((value) => {
          return <option  key={value._id} value={`${value.status},${value._id}`}>{value.status}</option>;
        })}
      </select>
    </>
  );
}
