"use client";
import React, {
  useEffect,
  useState,
  Fragment,
  useCallback,
  useRef,
} from "react";
import Sidebar from "../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import Tabs from "../components/tabs";
import axios from "axios";
import config from "../../../config.json";
import currency from "../../../Currency.json";
import {
  Dialog,
  Disclosure,
  Menu,
  Popover,
  Transition,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import {
  ArrowPathIcon,
  ChevronDownIcon,
  ChevronUpDownIcon,
} from "@heroicons/react/20/solid";
import Pagination from "../components/pagination";
import Link from "next/link";
import Cookies from "js-cookie";
import ConfirmationModal from "../components/confirmationModal";
import Loader from "../components/Loader";
import { ImSpinner8 } from "react-icons/im";
import { useDebounce } from "@/hooks/useDebounce";

const queryClient = new QueryClient();
export default function Shipments() {
  const [open, setOpen] = useState(false);
  const [shipmentData, setShipmentData] = useState([]);
  const [status, setStatus] = useState<any>([]);
  const [distributor, setDistributor] = useState<any>([]);
  const [activeFilters, setActiveFilters]: any = useState([]);
  const [filters, setFilters]: any = useState([]);
  const [selectedShipments, setSelectedShipments] = useState<string[]>([]);
  const [confirmationModalOpen, setConfirmationModalOpen]: any =
    useState(false);
  const [paginationCount, setPaginationCount]: any = useState(0);
  const [bulkProcessLoading, setBulkProcessLoading]: any = useState(false);
  const [bulkProcessError, setBulkProcessError]: any = useState(null);
  const [showBulkProcessError, setShowBulkProcessError]: any = useState(null);
  const [loader, setLoader]: any = useState(false);
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const debouncedValue = useDebounce(searchValue, 500);

  const handleSearchInput = useCallback((e) => {
    setSearchValue(e.target.value);
    setIsSearching(true);
  }, []);

  const handleRefreshButtonClick = () => {
    setRefreshButtonClicked(true);
    fetchShipmentData(1);
    fetchShipmentStatusData();
  };

  useEffect(() => {
    if (debouncedValue) {
      setAlreadyAppliedFilters((prev) => ({
        ...prev,
        input: debouncedValue,
      }));
    }
  }, [debouncedValue]);

  useEffect(() => {
    if (!searchValue) {
      setAlreadyAppliedFilters((prev) => ({
        ...prev,
        input: searchValue,
      }));
    }
  }, [searchValue]);

  const limit = 50;
  const filterSelected = (option: any) => {
    let arr = [option.value];
    setActiveFilters((activeFilters: any) => ({
      ...activeFilters,
      ...arr,
    }));
  };
  const removeFilter = (label: any) => {};
  function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
  }
  async function fetchShipmentData(page) {
    setLoader(true);
    try {
      let response = await axios.request({
        method: "get",
        url: `${config.BACKEND_URL}/shipment?page=${page}&limit=${limit}`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });

      const totalCount = Number(response.data.result);
      const numberOfPages = totalCount / limit;
      setPaginationCount(numberOfPages);

      let data = response.data.data.data.map((x: any) => {
        return {
          refID: x._id,
          id: x.name,
          ref: x.internalRef,
          status: x.status?.status,
          colorCode: x.status?.colorCode,
          date: new Date(x.createdAt).toString().slice(0, 15),
          from: "Sunrise WH",
          to: x?.order?.shipping_address?.address1,
          pseudoId: x.status.pseudoId,
        };
      });

      setShipmentData(data);
    } catch (error) {
      console.error("Error fetching shipment data:", error);
    } finally {
      setLoader(false);
      setRefreshButtonClicked(false);
    }
  }

  async function fetchShipmentStatusData() {
    setLoader(true);
    try {
      let response = await axios.request({
        method: "get",
        url: `${config.BACKEND_URL}/status`,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });
      let data = response.data.data.data.map((x: any) => ({
        status: x.status,
        id: x._id,
      }));

      //statusIdMap object is added for developers ease to know id refers to which status.
      const statusIdMap = {
        AwaitingCourierPartial: "673afbb0a7f9a80fac8a4944",
        AwaitingCourierFull: "673afb29907394a7758ae393",
        ShipmentCancelled: "674ef3d2ce77438d64f79cbe",
        Shipped: "674ef3ffce77438d64f79cbf",
      };

      // Get the list of IDs to filter
      const statusIdsToFilter = Object.values(statusIdMap);

      // Filter the data
      const filteredShipmentStatus = data.filter((entry: any) =>
        statusIdsToFilter.includes(entry.id)
      );

      setStatus(filteredShipmentStatus);
    } catch (error) {
      console.error("Error fetching shipment status data:", error);
    } finally {
      setLoader(false);
    }
  }

  // async function fetchDistributorData() {
  //   let response = await axios.request({
  //     method: "get",
  //     url: `${config.BACKEND_URL}/distributor`,
  //     headers: {
  //       "Content-Type": "application/json",
  //       Authorization: `Bearer ${Cookies.get("token")}`,
  //     },
  //   });
  //   let data = response.data.data.data
  //     .map((x: any) => ({
  //       name: x.name,
  //       id: x._id,
  //     }))
  //     .filter((value) => {
  //       return value.name != undefined;
  //     });

  //   setDistributor(data);
  // }

  const [inputFieldValue, setInputFieldValue] = useState<any>(null);
  const [flowType, setFlowType] = useState<any>(null);
  const [fromDate, setFromDate]: any = useState(null);
  const [toDate, setToDate]: any = useState(null);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedDistributors, setSelectedDistributors] = useState<string[]>(
    []
  );
  const [alreadyAppliedFilters, setAlreadyAppliedFilters]: any = useState([]);

  async function searchBarInput(page) {
    let input = inputFieldValue;
    const myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json");
    myHeaders.append("Authorization", `Bearer ${Cookies.get("token")}`);

    let raw = JSON.stringify({
      input: alreadyAppliedFilters?.input,
      distributor:
        alreadyAppliedFilters?.distributor?.length > 0
          ? alreadyAppliedFilters?.distributor
          : null,
      shipmentStatus:
        alreadyAppliedFilters?.shipmentStatus?.length > 0
          ? alreadyAppliedFilters?.shipmentStatus
          : null,
      fromDate: alreadyAppliedFilters?.fromDate,
      toDate: alreadyAppliedFilters?.toDate,
      flowType: alreadyAppliedFilters?.flowType,
      pageSize: 5,
      page: page,
    });

    if (alreadyAppliedFilters || alreadyAppliedFilters.length > 0) {
      Cookies.set("shipmentFilter", raw);
    }

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: Cookies.get("shipmentFilter") || raw,
    };

    const shipmentPromise = await fetch(
      `${config.BACKEND_URL}/shipmentSearch`,
      requestOptions
    );
    const response = await shipmentPromise.json();
    const totalCount = Number(response.total);
    const numberOfPages = totalCount / limit;
    setPaginationCount(numberOfPages);

    let data =
      response?.shipments?.length > 0 &&
      response?.shipments?.map((x: any) => {
        return {
          refID: x._id,
          id: x.name,
          ref: x.internalRef,
          status: x.status?.status,
          colorCode: x.status?.colorCode,
          date: new Date(x.createdAt).toString().slice(0, 15),
          from: "Sunrise WH",
          pseudoId: x.status.pseudoId,
          // to: x.order.shipping_address.address1,
        };
      });
    setShipmentData(data);
    setIsSearching(false);
  }

  useEffect(() => {
    fetchShipmentData(1);
    fetchShipmentStatusData();
    // fetchDistributorData();

    const savedFilters = Cookies.get("shipmentFilter");
    if (savedFilters) {
      // setAlreadyAppliedFilters(JSON.parse(savedFilters));
      Cookies.remove("shipmentFilter");
    }
  }, []);

  const handleChange = (e: any) => {
    const isChecked = e.target.checked;
    const option = e.target.value;

    setAlreadyAppliedFilters((prevState) => {
      const selectedOptionSet = new Set(prevState.shipmentStatus);
      if (isChecked) {
        selectedOptionSet.add(option);
      } else {
        selectedOptionSet.delete(option);
      }
      return { ...prevState, shipmentStatus: Array.from(selectedOptionSet) };
    });

    // const selectedOptionSet = new Set(selectedStatuses);

    // if (isChecked) {
    //   selectedOptionSet.add(option);
    // } else {
    //   selectedOptionSet.delete(option);
    // }

    // const newSelectedOptions = Array.from(selectedOptionSet);
    // setSelectedStatuses(newSelectedOptions);
  };

  const handleDistributorChange = (e: any) => {
    const isChecked = e.target.checked;
    const option = e.target.value;

    setAlreadyAppliedFilters((prevState) => {
      const selectedOptionSet = new Set(prevState.distributor);
      if (isChecked) {
        selectedOptionSet.add(option);
      } else {
        selectedOptionSet.delete(option);
      }
      return { ...prevState, distributor: Array.from(selectedOptionSet) };
    });

    // const selectedOptionSet = new Set(selectedDistributors);

    // if (isChecked) {
    //   selectedOptionSet.add(option);
    // } else {
    //   selectedOptionSet.delete(option);
    // }

    // const newSelectedOptions = Array.from(selectedOptionSet);
    // setSelectedDistributors(newSelectedOptions);
  };

  const [isInitialRender, setIsInitialRender] = useState(true);
  useEffect(() => {
    if (!isInitialRender) {
      searchBarInput(1);
    } else {
      setIsInitialRender(false);
    }
  }, [alreadyAppliedFilters]);

  // useEffect(() => {
  //   // if (isInitialRender) {
  //   //   setIsInitialRender(false);
  //   // } else {
  //     searchBarInput(1);
  //     setAlreadyAppliedFilters(JSON.parse(Cookies.get("shipmentFilter")));
  //   // }
  // }, [fromDate, toDate, inputFieldValue, selectedStatuses, selectedDistributors, flowType]);

  const handlePageChange = (page) => {
    searchBarInput(page);
  };

  const shipmentRef: any = useRef(null);
  const distributorRef: any = useRef(null);
  const [isShipmentDropdownOpen, setIsShipmentDropdownOpen] = useState(false);
  const [isDistributorDropdownOpen, setIsDistributorDropdownOpen] =
    useState(false);

  const handleClickOutside = (event) => {
    if (shipmentRef.current && !shipmentRef.current.contains(event.target)) {
      setIsShipmentDropdownOpen(false);
    }
    if (
      distributorRef.current &&
      !distributorRef.current.contains(event.target)
    ) {
      setIsDistributorDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    let timeout;
    if (bulkProcessError) {
      const error = bulkProcessError.split("@@");
      setShowBulkProcessError(error);
      timeout = setTimeout(() => {
        setBulkProcessError(null);
        setShowBulkProcessError(null);
      }, 7000);
    }
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [bulkProcessError]);

  const handleCheckbox = (
    e: React.ChangeEvent<HTMLInputElement>,
    id: string
  ) => {
    if (e.target.checked) {
      setSelectedShipments((prevSelectedIds) => [...prevSelectedIds, id]);
    } else {
      setSelectedShipments((prevSelectedIds) =>
        prevSelectedIds.filter((selectedId) => selectedId !== id)
      );
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <>
          <ConfirmationModal
            fetchData={fetchShipmentData}
            setBulkProcessError={setBulkProcessError}
            setBulkProcessLoading={setBulkProcessLoading}
            open={confirmationModalOpen}
            setOpen={setConfirmationModalOpen}
            selected={selectedShipments}
            setSelected={setSelectedShipments}
            passedItems={shipmentData}
            itemType="Shipment"
          />
          <div className="flex p-4 items-center">
            <h2 className="text-3xl w-[95%] font-semibold leading-6 text-gray-900">
              Shipment
            </h2>
            <button
              type="button"
              className="flex justify-around w-[5%] rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95   focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              onClick={handleRefreshButtonClick}
            >
              <ArrowPathIcon
                className={`${
                  refreshButtonClicked ? "animate-spin" : "animate-none"
                } h-5 w-5`}
              />
            </button>
          </div>
          <div className="mt-4 bg-white rounded-lg shadow">
            <Transition.Root show={open} as={Fragment}>
              <Dialog className="relative z-40 sm:hidden" onClose={setOpen}>
                <Transition.Child
                  as={Fragment}
                  enter="transition-opacity ease-linear duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="transition-opacity ease-linear duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="fixed inset-0 bg-black bg-opacity-25" />
                </Transition.Child>
                <div className="fixed inset-0 z-40 flex">
                  <Transition.Child
                    as={Fragment}
                    enter="transition ease-in-out duration-300 transform"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transition ease-in-out duration-300 transform"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="relative ml-auto flex h-full w-full max-w-xs flex-col overflow-y-auto bg-white py-4 pb-12 shadow-xl">
                      <div className="flex items-center justify-between px-4">
                        <button
                          type="button"
                          className="-mr-2 flex h-10 w-10 items-center justify-center rounded-md bg-white p-2 text-gray-400"
                          onClick={() => setOpen(false)}
                        >
                          <span className="sr-only">Close menu</span>
                          <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                      </div>
                      <form className="mt-4">
                        {filters.map((section) => (
                          <Disclosure
                            as="div"
                            key={section.name}
                            className="border-t border-gray-200 px-4 py-6"
                          >
                            {({ open }) => (
                              <>
                                <h3 className="-mx-2 -my-3 flow-root">
                                  <Disclosure.Button className="flex w-full items-center justify-between bg-white px-2 py-3 text-sm text-gray-400">
                                    <span className="font-medium text-gray-900">
                                      {section.name}
                                    </span>
                                    <span className="ml-6 flex items-center">
                                      <ChevronDownIcon
                                        className={classNames(
                                          open ? "-rotate-180" : "rotate-0",
                                          "h-5 w-5 transform"
                                        )}
                                        aria-hidden="true"
                                      />
                                    </span>
                                  </Disclosure.Button>
                                </h3>
                                <Disclosure.Panel className="pt-6">
                                  <div className="space-y-6">
                                    {section.options.map(
                                      (option: any, optionIdx: any) => (
                                        <div
                                          key={option.value}
                                          className="flex items-center"
                                        >
                                          <input
                                            id={`filter-mobile-${section.id}-${optionIdx}`}
                                            name={`${section.id}[]`}
                                            defaultValue={option.value}
                                            type="checkbox"
                                            defaultChecked={option.checked}
                                            className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                          />
                                          <label
                                            htmlFor={`filter-mobile-${section.id}-${optionIdx}`}
                                            className="ml-3 text-sm text-[#222222]"
                                          >
                                            {option.label}
                                          </label>
                                        </div>
                                      )
                                    )}
                                  </div>
                                </Disclosure.Panel>
                              </>
                            )}
                          </Disclosure>
                        ))}
                      </form>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </Dialog>
            </Transition.Root>

            {/* <button
                  type="button"
                  className="flex justify-around w-[10%] rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  onClick={() => fetchShipmentData()}>
                 <ArrowPathIcon className={classNames("h-5 w-5 transform")} /> Refresh
                </button> */}

            {/* <div className="flex justify-end px-9">
                <button
                  type="button"
                  className="flex justify-around w-[5%] rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  onClick={() => fetchShipmentData()}>
                  <ArrowPathIcon className={classNames("h-5 w-5 transform")} />
                </button>
          </div> */}

            <section aria-labelledby="filter-heading">
              <div className="border-b rounded-lg border-gray-200 bg-white pb-4 px-2">
                {/* //Commented to hide bulk process errors
              {bulkProcessError != null &&
                showBulkProcessError &&
                showBulkProcessError.length > 0 &&
                showBulkProcessError.map((line: any, index: any) => {
                  return (
                    <p
                      className="text-red-600 text-right font-bold"
                      key={`${index}-${line}`}
                    >
                      {line}
                    </p>
                  );
                })} */}
                <div className="mx-auto flex flex-row w-full gap-4 px-4 sm:px-6 lg:px-0">
                  <div className="flex-1 flex flex-col justify-end">
                    <label>Search</label>
                    <div className="relative">
                      <input
                        type="text"
                        name="email"
                        id="searchBarInput"
                        className="w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        placeholder="Shipment name"
                        value={searchValue}
                        onChange={handleSearchInput}
                      />
                      {isSearching && (
                        <span className="absolute right-2 top-2">
                          <ImSpinner8
                            fill="black"
                            size={20}
                            className={`${
                              isSearching ? "animate-spin" : "animate-none"
                            }`}
                          />
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex-1 flex flex-row gap-3 items-end justify-end">
                    <div className="mt-2" ref={shipmentRef}>
                      <label className="whitespace-nowrap cursor-pointer p-3 relative flex items-center w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                        <input
                          type="checkbox"
                          className="hidden peer"
                          checked={isShipmentDropdownOpen}
                          onChange={() =>
                            setIsShipmentDropdownOpen(!isShipmentDropdownOpen)
                          }
                        />
                        {"Select Shipment Status"}

                        <div className="h-[220px] overflow-y-scroll top-full left-0 absolute cursor-pointer bg-white border transition-opacity opacity-0 pointer-events-none peer-checked:opacity-100 peer-checked:pointer-events-auto">
                          <ul>
                            {status.map((option, i: number) => {
                              return (
                                <li key={i}>
                                  <label className="flex items-center p-3 whitespace-nowrap cursor-pointer transition-colors hover:bg-blue-100 [&:has(input:checked)]:bg-blue-200">
                                    <input
                                      type="checkbox"
                                      name={"status"}
                                      value={option.id}
                                      className="cursor-pointer"
                                      checked={alreadyAppliedFilters?.shipmentStatus?.includes(
                                        option.id
                                      )}
                                      onChange={handleChange}
                                    />
                                    <span className="ml-1">
                                      {option.status}
                                    </span>
                                  </label>
                                </li>
                              );
                            })}
                          </ul>
                        </div>

                        <ChevronDownIcon
                          className={"h-5 w-5 ml-3 transform"}
                          aria-hidden="true"
                        />
                      </label>
                    </div>

                    <div className="mt-2">
                      <label>From</label>
                      <input
                        type="date"
                        // onChange={(e) => setFromDate(e.target.value)}
                        value={alreadyAppliedFilters?.fromDate}
                        onChange={(e) =>
                          setAlreadyAppliedFilters((prev) => ({
                            ...prev,
                            fromDate: e.target.value,
                          }))
                        }
                        className="block cursor-pointer rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      />
                    </div>
                    <div className="mt-2">
                      <label>To</label>
                      <input
                        type="date"
                        // onChange={(e) => setToDate(e.target.value)}
                        value={alreadyAppliedFilters?.toDate}
                        onChange={(e) =>
                          setAlreadyAppliedFilters((prev) => ({
                            ...prev,
                            toDate: e.target.value,
                          }))
                        }
                        className="block cursor-pointer rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      />
                    </div>
                    <div className="relative mt-auto mx-1 inline-block">
                      <button
                        onClick={() => {
                          setBulkProcessLoading(true);
                          setConfirmationModalOpen(true);
                        }}
                        type="button"
                        className="rounded-md whitespace-nowrap bg-gray-700 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                      >
                        Bulk process
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              {activeFilters.length > 0 ? (
                <div className="bg-gray-100">
                  <div className="mx-auto px-4 py-3 sm:px-6 lg:px-8">
                    <div className="mt-2 sm:ml-4 sm:mt-0">
                      <div className="-m-1 flex flex-wrap items-center">
                        {activeFilters.map((activeFilter: any) => (
                          <span
                            key={activeFilter.value}
                            className="m-1 inline-flex items-center rounded-full border border-gray-200 bg-white py-1.5 pl-3 pr-2 text-sm font-medium text-gray-900"
                          >
                            <span>{activeFilter.label}</span>
                            <button
                              type="button"
                              onClick={() => {
                                removeFilter(activeFilter.label);
                              }}
                              className="ml-1 inline-flex h-4 w-4 flex-shrink-0 rounded-full p-1 text-gray-400 hover:bg-gray-200 hover:text-[#222222]"
                            >
                              <span className="sr-only">
                                Remove filter for {activeFilter.label}
                              </span>
                              <svg
                                className="h-2 w-2"
                                stroke="currentColor"
                                fill="none"
                                viewBox="0 0 8 8"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeWidth="1.5"
                                  d="M1 1l6 6m0-6L1 7"
                                />
                              </svg>
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div></div>
              )}
            </section>
          </div>
          <div className="">
            <div className="mt-8 flow-root">
              <div className="-my-2 overflow-x-auto">
                <div className="inline-block min-w-full py-2 align-middle">
                  {/* <button
                  type="button"
                  className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                  Process order
                </button> */}
                  <table className="table-fixed rounded-lg shadow-sm text-center w-full divide-y divide-gray-300">
                    <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th className="w-[50px]"></th>
                        <th
                          scope="col"
                          className="py-3.5 pl-4 pr-3 text-sm font-semibold text-white sm:pl-3"
                        >
                          Shipment Name
                        </th>
                        {/* <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Reference
                      </th> */}
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold text-white w-[400px]"
                        >
                          Shipment status
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-sm font-semibold text-white"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-sm font-semibold text-white"
                        >
                          From
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {loader ? (
                        <tr>
                          <td colSpan={5} className="py-6 text-center">
                            <ImSpinner8
                              fill="black"
                              size={40}
                              className=" animate-spin inline-block"
                            />
                          </td>
                        </tr>
                      ) : (
                        <ShipmentTableBody
                          shipmentData={shipmentData}
                          selectedShipments={selectedShipments}
                          handleCheckbox={handleCheckbox}
                        />
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            {paginationCount > 1 && (
              <Pagination
                paginationCount={Math.ceil(paginationCount)}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </>
      </Sidebar>
    </QueryClientProvider>
  );
}

function ShipmentTableBody(props) {
  const { shipmentData, selectedShipments, handleCheckbox } = props;
  return (
    <>
      {shipmentData.length > 0 &&
        shipmentData?.map((x: any, i: any) => (
          <tr key={i} className="even:bg-gray-50">
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium sm:pl-3">
              <input
                type="checkbox"
                disabled={
                  x.pseudoId.toUpperCase() !== "AWAITING_COURIER_PARTIAL"
                }
                className={
                  x.pseudoId.toUpperCase() !== "AWAITING_COURIER_PARTIAL"
                    ? "bg-slate-400"
                    : ""
                }
                checked={selectedShipments.includes(x.refID)}
                onChange={(e) => handleCheckbox(e, x.refID)}
              />
            </td>
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-blue-500 sm:pl-3">
              <Link href={`/shipments/details/${x.refID}`}>{x.id}</Link>
            </td>
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
              <div
                style={{ backgroundColor: `${x.colorCode}` }}
                className="px-4 py-2 text-center rounded-lg"
              >
                {x.status}
              </div>
            </td>
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
              {x.date}
            </td>
            <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
              {x.from}
            </td>
          </tr>
        ))}
    </>
  );
}
