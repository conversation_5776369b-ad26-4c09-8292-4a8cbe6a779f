"use client";
import React, { useEffect, useState, Fragment, useRef } from "react";
import Sidebar from "../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../../config.json";
const queryClient = new QueryClient();
export default function StatusFlow() {
  const [statusData, setStatusData] = useState([]);
  const [statusDataFlow, setStatusDataFlow] = useState([]);
  const [open, setOpen] = useState(false);
  const cancelButtonRef = useRef(null);
  async function deleteFlow(id: any) {
    await axios.request({
      method: "delete",
      url: `${config.BACKEND_URL}/status_flow/${id}`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    fetchStatusFlowData();
    fetchStatusData();
  }
  async function sendStatusFlowCreation() {
    await axios.request({
      method: "post",
      url: `${config.BACKEND_URL}/status_flow`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        transitionName: (
          document.getElementById("transitionName") as HTMLFormElement
        ).value,
        statusId: (document.getElementById("status_from") as HTMLFormElement)
          .value,
        toStatusId: (document.getElementById("status_to") as HTMLFormElement)
          .value,
      }),
    });
    fetchStatusFlowData();
    fetchStatusData();
  }
  async function fetchStatusFlowData() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/status_flow`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        id: x._id,
        transitionName: x.transitionName,
        from: x.statusId.status,
        to: x.toStatusId.status,
      };
    });
    setStatusDataFlow(data);
  }
  async function fetchStatusData() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/status`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    setStatusData(response.data.data.data);
  }
  useEffect(() => {
    fetchStatusData();
    fetchStatusFlowData();
  }, []);
  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            className="relative z-10"
            initialFocus={cancelButtonRef}
            onClose={setOpen}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0">
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                  <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div>
                      <div className="mt-3 text-center sm:mt-5">
                        <div className="isolate -space-y-px rounded-md shadow-sm">
                          <div className="relative">
                            <label
                              htmlFor="transitionName"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Transition name
                            </label>
                            <input
                              type="text"
                              name="transitionName"
                              id="transitionName"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="status_from"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Status from
                            </label>
                            <select
                              id="status_from"
                              name="status_from"
                              className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                              {statusData.map((x: any) => (
                                <option value={x._id} key={x._id}>
                                  {x.status}
                                </option>
                              ))}
                            </select>
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="status_to"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900">
                              Status to
                            </label>
                            <select
                              id="status_to"
                              name="status_to"
                              className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6">
                              {statusData.map((x: any) => (
                                <option value={x._id} key={x._id}>
                                  {x.status}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 sm:mt-6 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                        onClick={() => {
                          sendStatusFlowCreation();
                          setOpen(false);
                        }}>
                        Create
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto"></div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                type="button"
                onClick={() => {
                  setOpen(true);
                }}
                className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                Create status transition
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead>
                    <tr>
                      <th
                        scope="col"
                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                        Name
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Status from
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Status to
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {statusDataFlow.map((x: any) => (
                      <tr key={x.id} className="even:bg-gray-50">
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.transitionName}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.from}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.to}
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                          <button
                            onClick={() => {
                              deleteFlow(x.id);
                            }}
                            type="button"
                            className="block rounded-md bg-red-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </Sidebar>
    </QueryClientProvider>
  );
}
