import React, { useState, useEffect, useCallback } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import config from "../../../config.json";

type UserGroup = {
  _id: string;
  name: string;
};

type FormData = {
  name: string;
  username: string;
  userGroup: string;
};

export default function UpdateUserForm({ editId, fetchUserData, userGroupData, setOpen }: { editId: string, fetchUserData: any, userGroupData:any, setOpen: any }) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    username: "",
    userGroup: "",
  });

  const fetchData = useCallback(async (id: string) => {
    const response = await fetch(
      `${config.BACKEND_URL}/user/${id}`
    );
    if (!response.ok) throw new Error("Network response was not ok");
    const res = await response.json();
    delete res.data.data.password;
    setFormData(res.data.data);
    return res;
  }, []);

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ["users", editId],
    queryFn: () => fetchData(editId),
  });

  // Mutation for updating user details
  const updateUser = useMutation({
    mutationFn: (data: FormData) =>
      fetch(
        `${config.BACKEND_URL}/user/${editId}`,
        {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data),
        }
      ).then((response) => {response.json(); setOpen(false); fetchUserData(); }),
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleUserGroupChange = (
    event: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      userGroup: event.target.value,
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    updateUser.mutate(formData);
  };


  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error fetching data: {error.message}</div>;

  return (
    <form onSubmit={handleSubmit}>
      <div className="pb-6">
        <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Email
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="email"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
            />
          </div>
          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Name
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
            />
          </div>

          <div className="sm:col-span-3">
                <label className="block text-sm font-medium leading-6 text-gray-900">Update Password (leave blank to keep the same)</label>
                <input 
                  className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  type="password" name="password" onChange={handleInputChange} />
            </div>
          {/* <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              User Role
            </label>
            <select value={formData.userGroup} className="w-full text-black" onChange={handleUserGroupChange}>
              <option value="">Select User Role</option>
              {userGroupData.map((group) => (
                <option key={group._id} value={group._id}>
                  {group.name}
                </option>
              ))}
            </select>
          </div> */}

          <div className="mt-6 flex items-center justify-center gap-x-6">
            <button
              type="button"
              className="text-sm font-semibold leading-6 w-[30%] text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
              onClick={() => setOpen(false)}
            > 
              Cancel
            </button>
            <button
              type="submit"
              className="rounded-md w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
            >
              Update User
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
