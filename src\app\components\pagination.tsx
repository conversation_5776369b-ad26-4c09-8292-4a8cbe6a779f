import React, { useState } from "react";

export default function Pagination({ paginationCount, onPageChange }) {
  const [currentPage, setCurrentPage] = useState(1);

  const handlePageClick = (page) => {
    setCurrentPage(page);
    if (onPageChange) {
      onPageChange(page);
    }
  };

  return (
    <div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-center my-4">
        <div>
          <nav
            className="isolate inline-flex rounded-md shadow-sm"
            aria-label="Pagination"
          >
            {Array.from({ length: paginationCount }, (_, index) => {
              const page = index + 1;
              return (
                <span
                  key={page}
                  aria-current={currentPage === page ? "page" : undefined}
                  className={`relative rounded inline-flex items-center px-4 py-2 mr-3 text-sm font-semibold cursor-pointer ${
                    currentPage === page
                      ? "z-10 bg-[#373435] text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                      : "text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-[#222222] focus:z-20 focus:outline-offset-0"
                  }`}
                  onClick={() => handlePageClick(page)}
                >
                  {page}
                </span>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );
}
