"use client";

import React, { useEffect, useState } from "react";
import Sidebar from "./components/sidebar";
import Cookies from "js-cookie";
import Report from "./components/report";
import ReportFour from "./components/reportFour";
import ReportTwo from "./components/reportTwo";
import ReportThree from "./components/reportThree";
import SunriseBlack from "./assets/Sunrise_TradeWhite.png";
import HomeBanner from "./assets/Sunrise_tradeHomebanner.jpg";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import config from "../../config.json";
import axios from "axios";
import SuccessModal from "./components/successModal";
import SuccessNotification from "./components/SuccessNotifications";

export default function Home() {
  const [buttonLoader, setButtonLoader] = useState(true);
  const [message, setMessage] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);

  const productSyncManually = async () => {
    try {
      setButtonLoader(true);
      const response = await axios.post(`${config.BACKEND_URL}/cron`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });

      if (response.data.status === "success") {
        setShowSuccess(true);
        setMessage(response.data?.data?.message);
      }
    } catch (error) {
      setButtonLoader(false);
      console.error("Error syncing products:", error);
    }
  };

  useEffect(() => {
    let intervalId;

    const pollCronStatus = async () => {
      const response = await axios.get(`${config.BACKEND_URL}/cron`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const loading = response?.data?.data?.loading;
      setButtonLoader(loading);

      // Stop polling if it's no longer loading
      if (!loading) {
        clearInterval(intervalId);
      }
    };

    pollCronStatus();
    intervalId = setInterval(pollCronStatus, 10000);

    return () => clearInterval(intervalId);
  }, []);

  return (
    <Sidebar>
      {/* <Report type={"orderDistributorsManagersBreakdown"} title={"Orders Summary: Distributors & Managers Breakdown (last 30 days)"} />
      <br /> */}
      {/* <br />
      <Report
        type={"shipmentsAligned"} 
        title={"Orders Summary: Alignment-wise Analytics (last 30 days)"} 
      />
      <br />
      <br />
      <ReportThree 
        type={"shipmentsSummary"} 
        title={"Shipments Summary: Domestic Divergence Analytics (last 30 days)"} 
      />
      <br />
      <br />
      <ReportFour />
      <br />
      <br /> */}
      {showSuccess && <SuccessNotification message={message} />}

      <div className="w-full h-[100vh] flex items-center justify-center bg-white">
        <img
          className="w-full h-[100vh] object-cover object-center"
          src={HomeBanner.src}
          alt="Home Banner"
        />
      </div>
      <ToastContainer />
    </Sidebar>
  );
}
