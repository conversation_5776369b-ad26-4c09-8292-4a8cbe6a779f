"use client";

import React, { useCallback, useEffect, useState } from "react";
import {
  QueryClientProvider,
  QueryClient,
  useQuery,
} from "@tanstack/react-query";
import Sidebar from "@/app/components/sidebar";
import { PencilIcon, TrashIcon } from "@heroicons/react/20/solid";
import RolesModal from "@/app/components/rolesModal";
import config from "../../../../config.json";
import { DeleteConfirmationModal } from "@/app/components/deleteConfirmationModal";
import Cookies from "js-cookie";

interface Person {
  _id: any;
  email?: string;
  name: string;
  description?: string;
}

interface ApiResponse {
  data: Person[];
}

const queryClient = new QueryClient();

export default function RolesPage({ pageData }) {
  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState(false);
  const [editId, setEditId] = useState();
  const [error, setError]: any = useState();
  const [addNewUser, setAddNewUser] = useState(false);

  const [data, setData] = useState(pageData.data);

  const fetchData = () => {
    const requestOptions = {
      method: "GET",
      headers: {
        Authorization: `Bearer ${Cookies.get("token")}`,
      }
    };

    fetch(`${config.BACKEND_URL}/usergroup`, requestOptions)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json();
      })
      .then((result) => {
        setData({ data: result.data.data });
      })
      .catch((error) => {
        setData(null);
      });
  };

  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [confirmationOpen, setConfirmationOpen] = useState(false);

  function deleteUser(id: any) {
    setCurrentId(id);
    setCurrentType("usergroup");
    setConfirmationOpen(true);
  }

  const [newScopes, setNewScopes]: any = useState({});

  useEffect(() => {
    const scopes = Cookies.get("type");
    const getScopes = scopes && JSON.parse(scopes);
    setNewScopes(getScopes);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h1 className="text-2xl font-semibold leading-6 text-gray-900">
                Roles
              </h1>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                onClick={() => {
                  setAddNewUser(true);
                  setOpen(true);
                  setEdit(false);
                }}
              >
                Add Roles
              </button>
            </div>
          </div>

          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                    <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                      <tr>
                        <th
                          scope="col"
                          className="rounded-tl-lg py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                        >
                          S. NO
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Description
                        </th>
                        {(newScopes &&
                          newScopes?.user?.length > 0 &&
                          newScopes?.user?.includes("write")) ||
                        newScopes?.user?.includes("delete") ? (
                          <th
                            scope="col"
                            className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Action
                          </th>
                        ) : (
                          ""
                        )}
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {data != null && (
                        <>
                          {data?.data.map((person, index) => (
                            <tr key={person.email}>
                              <td className="whitespace-nowrap py-3.5 pl-4 pr-3 text-sm text-[#222222]">
                                {index + 1}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                                {person.name}
                              </td>
                              <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">
                                {person?.description}
                              </td>
                              {newScopes &&
                                newScopes.user?.length > 0 &&
                                (newScopes.user.includes("write") ||
                                  newScopes.user.includes("delete")) &&
                                (!person.name.includes("Super Admin") ? (
                                  // Render buttons if not "Super Admin"
                                  <td className="relative text-left whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-6">
                                    {/* Edit button */}
                                    {newScopes.user.includes("write") && (
                                      <button
                                        type="button"
                                        className="rounded-full bg-[#222222] p-2 text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                                        onClick={() => {
                                          setOpen(!open);
                                          setEdit(true);
                                          setEditId(person._id);
                                        }}
                                      >
                                        <PencilIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    )}

                                    {/* Delete button */}
                                    {newScopes.user.includes("delete") && (
                                      <button
                                        type="button"
                                        className="rounded-full bg-[#222222] p-2 ml-4 text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                                        onClick={() => deleteUser(person._id)}
                                      >
                                        <TrashIcon
                                          className="h-5 w-5"
                                          aria-hidden="true"
                                        />
                                      </button>
                                    )}
                                  </td>
                                ) : (
                                  <td className="py-4 pl-3 pr-4 text-sm font-medium sm:pr-6"></td>
                                ))}
                            </tr>
                          ))}
                        </>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchData}
            // data={data}
            setTableData={setData}
          />
        )}
        <RolesModal
          error={error}
          setError={setError}
          open={open}
          setOpen={setOpen}
          updateRole={edit}
          editId={editId}
          fetchData={fetchData}
          addNewRole={addNewUser}
        />
      </Sidebar>
    </QueryClientProvider>
  );
}
