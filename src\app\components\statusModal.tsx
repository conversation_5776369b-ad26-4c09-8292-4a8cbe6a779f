"use client";

import { useRef, useState, Fragment, useReducer, useId } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { MultiSelect } from "react-multi-select-component";
import config from "../../../config.json";
import Cookies from "js-cookie";

type FormState = {
  status: string;
  statusType: string;
  flowType: string;
  departmentActionable: string;
  escalationDepartment: string;
  departmentNotification: { label: string; value: string }[];
  escalationTriggerPeriod: number;
  colorCode: string;
  error: string;
};

type Action =
  | { type: "SET_FIELD"; field: keyof FormState; value: any }
  | { type: "RESET" }
  | { type: "SET_ERROR"; value: string };

const initialFormState: FormState = {
  status: "",
  statusType: "",
  flowType: "",
  departmentActionable: "",
  escalationDepartment: "",
  departmentNotification: [],
  escalationTriggerPeriod: 1,
  colorCode: "#000000",
  error: "",
};

function formReducer(state: FormState, action: Action): FormState {
  switch (action.type) {
    case "SET_FIELD":
      return { ...state, [action.field]: action.value, error: "" };
    case "RESET":
      return initialFormState;
    case "SET_ERROR":
      return { ...state, error: action.value };
    default:
      return state;
  }
}

export default function StatusModal({
  departmentOptions,
  setOpen,
  open,
  fetchStatusData,
  fetchDepartmentData,
}) {
  const cancelButtonRef = useRef(null);
  const [departmentNotification, setDepartmentNotification] = useState([]);
  const [state, dispatch] = useReducer(formReducer, initialFormState);

  const statusId = useId();
  const statusTypeId = useId();
  const flowTypeId = useId();
  const departmentActionableId = useId();
  const escalationDepartmentId = useId();
  const escalationPeriodId = useId();
  const colorCodeId = useId();

  const validateForm = () => {
    if (
      !state.status.trim() ||
      !state.statusType.trim() ||
      !state.flowType.trim() ||
      !state.departmentActionable.trim() ||
      !state.escalationDepartment.trim() ||
      departmentNotification.length === 0 ||
      state.escalationTriggerPeriod <= 0 ||
      state.escalationTriggerPeriod > 90 ||
      !state.colorCode.trim()
    ) {
      dispatch({
        type: "SET_ERROR",
        value: "Please fill all fields with valid inputs.",
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      const response = await fetch(
        `${config.BACKEND_URL}/status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          body: JSON.stringify({
            status: state.status,
            statusType: state.statusType,
            flowType: state.flowType,
            departmentActionable: state.departmentActionable,
            departmentType: state.departmentActionable,
            departmentNotified: departmentNotification,
            escalationTriggerPeriod: state.escalationTriggerPeriod,
            colorCode: state.colorCode,
            escalationDepartment: state.escalationDepartment,
          }),
        }
      );

      const data = await response.json();

      if (data.status === "fail") {
        dispatch({ type: "SET_ERROR", value: data.message });
        console.error(data.message);
        return;
      }

      // Reset the form on success
      dispatch({ type: "RESET" });
      fetchStatusData();
      fetchDepartmentData();
      setOpen(false);
    } catch (err) {
      dispatch({
        type: "SET_ERROR",
        value: "An error occurred while creating the status.",
      });
      console.error(err);
    }
  };

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        className="relative z-10"
        initialFocus={cancelButtonRef}
        onClose={setOpen}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>
        <div className="fixed inset-0 z-10 w-screen overflow-y-scroll pt-12">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <label
                      htmlFor={statusId}
                      className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Status
                    </label>
                    <input
                      type="text"
                      name="status"
                      id={statusId}
                      value={state.status}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "status",
                          value: e.target.value,
                        })
                      }
                      className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    />
                  </div>
                  <br></br>

                  <div className="relative">
                    <label
                      htmlFor={statusTypeId}
                      className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Status type
                    </label>
                    <select
                      id={statusTypeId}
                      value={state.statusType}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "statusType",
                          value: e.target.value,
                        })
                      }
                      className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    >
                      <option value="">Select Status Type</option>
                      <option value="Order">Order</option>
                      <option value="Shipment">Shipment</option>
                    </select>
                  </div>
                  <br></br>

                  <div className="relative">
                    <label
                      htmlFor={departmentActionableId}
                      className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Department actionable
                    </label>
                    <select
                      id={departmentActionableId}
                      value={state.departmentActionable}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "departmentActionable",
                          value: e.target.value,
                        })
                      }
                      className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    >
                      <option value={"Select Department Notified"}>
                        Select Department Actionable
                      </option>
                      {departmentOptions.map((x: any) => (
                        <option value={x.optionKey} key={x.optionKey}>
                          {x.optionName}
                        </option>
                      ))}
                    </select>
                  </div>
                  <br></br>
                  <div className="relative">
                    <label
                      htmlFor="departmentNotified"
                      className="absolute z-10 -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Department notified
                    </label>
                    <MultiSelect
                      className="mt-2 block w-full rounded-md   text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      options={departmentOptions?.map((x) => ({
                        label: x?.optionName,
                        value: x?.optionKey,
                      }))}
                      value={departmentNotification}
                      onChange={(selected: any) => {
                        setDepartmentNotification(selected);
                        console.log(selected);
                      }}
                      labelledBy="Department notified"
                    />
                  </div>
                  <br></br>

                  <div className="relative">
                    <label
                      htmlFor={escalationDepartmentId}
                      className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Escalation Department
                    </label>
                    <select
                      id={escalationDepartmentId}
                      value={state.escalationDepartment}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "escalationDepartment",
                          value: e.target.value,
                        })
                      }
                      className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    >
                      <option value={"Select Department Notified"}>
                        Select Escalation Department
                      </option>
                      {departmentOptions.map((x: any) => (
                        <option value={x.optionKey} key={x.optionKey}>
                          {x.optionName}
                        </option>
                      ))}
                    </select>
                  </div>

                  <br></br>
                  <div>
                    <label
                      htmlFor={flowTypeId}
                      className="block text-sm font-medium text-gray-900"
                    >
                      Flow Type
                    </label>
                    <select
                      id={flowTypeId}
                      value={state.flowType}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "flowType",
                          value: e.target.value,
                        })
                      }
                      className="block w-full text-black rounded-md border py-2 px-3"
                    >
                      <option value="">Select Flow Type</option>
                      <option value="happy">Happy</option>
                      <option value="unhappy">Unhappy</option>
                    </select>
                  </div>
                  <br></br>

                  <div className="relative">
                    <label
                      htmlFor={escalationPeriodId}
                      className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                    >
                      Escalation Period (Max 90 Days)
                    </label>
                    <input
                      type="number"
                      className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                      name="escalationPeriod"
                      id={escalationPeriodId}
                      value={state.escalationTriggerPeriod}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "escalationTriggerPeriod",
                          value: Number(e.target.value),
                        })
                      }
                      min={1}
                      max={90}
                    />
                  </div>
                  <br></br>

                  <div className="flex w-full items-center pb-4 pt-2">
                    <hr className="w-48"></hr>
                    <p className=" text-sm text-gray-900">
                      <b>Extra fields</b>
                    </p>
                    <hr className="w-48"></hr>
                  </div>

                  <div>
                    <label
                      htmlFor={colorCodeId}
                      className="block text-sm font-medium text-gray-900"
                    >
                      Color Code
                    </label>
                    <input
                      type="color"
                      id={colorCodeId}
                      value={state.colorCode}
                      onChange={(e) =>
                        dispatch({
                          type: "SET_FIELD",
                          field: "colorCode",
                          value: e.target.value,
                        })
                      }
                      className="w-20 h-10 text-black"
                    />
                  </div>
                  <br></br>

                  {state.error && <p className="text-red-500">{state.error}</p>}

                  <div className="flex justify-center">
                    <button
                      type="button"
                      className="inline-flex w-[30%] mr-4 justify-center text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md text-sm font-semibold shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2  sm:col-start-2"
                      onClick={() => dispatch({ type: "RESET" })}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="inline-flex w-[30%] justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2  sm:col-start-2"
                    >
                      Create
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
