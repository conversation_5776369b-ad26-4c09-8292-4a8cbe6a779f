import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useState } from "react";
import config from "../../../config.json";
import Cookies from "js-cookie";

type Access = {
  read: string;
  write: string;
  delete: string;
};

type FormData = {
  name: string;
  description: string;
  access_scopes: any;
};

export default function NewRolesForm({
  fetchData,
  setOpen 
}) {
  const [error, setError] = useState("");
  const formFields = [
    "department",
    "designation",
    "distributor",
    "order",
    "shipment",
    "organization",
    "status",
    "user",
    "user_group",
    "statusFlow",
  ];

  const newRole = useMutation({
    mutationFn: async (newData: FormData) => {
      const requestOptions: RequestInit = {
        method: "POST",
        headers: {
          Authorization: `Bearer ${Cookies.get("token")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(newData),
      };

      const response = await fetch(
        `${config.BACKEND_URL}/usergroup`,
        requestOptions
      );
      const newResponse = await response.json();

      if (newResponse.error) {
        setError(newResponse.error);
        setOpen(true);
      } else {
        setError("");
        setOpen(false);
      }

      fetchData();
      return await response.json();
    },
  });

  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    access_scopes: {},
  });

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    field?: string,
    permission?: "read" | "write" | "delete"
  ) => {
    const { checked } = event.target;

    if (field && permission) {
      setFormData((prev) => {
        const newAccessScopes = { ...prev.access_scopes };

        // Ensure the field is always an array
        if (!Array.isArray(newAccessScopes[field])) {
          newAccessScopes[field] = [];
        }

        if (checked) {
          // Add the permission if not already included
          if (!newAccessScopes[field].includes(permission)) {
            newAccessScopes[field] = [...newAccessScopes[field], permission];
          }
        } else {
          // Remove the permission if unchecked
          newAccessScopes[field] = newAccessScopes[field].filter(
            (perm) => perm !== permission
          );
        }

        return {
          ...prev,
          access_scopes: newAccessScopes,
        };
      });
    } else {
      setFormData((prev) => ({
        ...prev,
        [event.target.name]: event.target.value,
      }));
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    if (
      !formData.name.trim() ||
      Object.keys(formData.access_scopes).length == 0
    ) {
      setError("Please Fill all the fields");
      return;
    }
    newRole.mutate(formData);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <h2 className="text-center font-semibold leading-7 text-gray-900">
              New Role
            </h2>
          </div>

          <div className="pb-6">
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
              <div className="sm:col-span-3">
                <label
                  htmlFor="roleName"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Role Name
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="name"
                    id="roleName"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <label
                  htmlFor="description"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Description
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="description"
                    id="description"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <label
                  htmlFor="access-control"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Select Access Control
                </label>
              </div>
              <div className="flex">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead>
                    <tr>
                      <th
                        scope="col"
                        className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0"
                      ></th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        READ
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        WRITE
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        DELETE
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {formFields.map((field) => (
                      <tr key={field}>
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">
                          {field}
                        </td>
                        {["read", "write", "delete"].map((perm) => (
                          <td
                            key={perm}
                            className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]"
                          >
                            <input
                              name={field}
                              type="checkbox"
                              onChange={(e) =>
                                handleInputChange(
                                  e,
                                  field,
                                  perm as "read" | "write" | "delete"
                                )
                              }
                              className="cursor-pointer"
                            />
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {error && <p className="text-red-600 font-bold">{error}</p>}
        <div className="mt-6 flex items-center justify-center gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md w-[30%]"
            onClick={() => setOpen(false)}
          >
            {" "}
            Cancel{" "}
          </button>
          <button
            type="submit"
            className="rounded-md w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
}
