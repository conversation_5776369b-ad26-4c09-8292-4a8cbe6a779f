import React from "react";

import Image from "next/image";
import Logo from "../../assets/Sunrise-logo.png";
import SunriseBlack from "../../assets/Sunrise_TradeWhite.png";
import LoginForm from "../../components/loginForm";
import SunriseBanner from "../../assets/Sunrise_trade_banner.jpg";

const tabs = [
  { name: "Department", href: "/login/department", current: false },
  { name: "Admin", href: "/login/admin", current: true },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

const Login: React.FC = () => {
  return (
    <div className="relative min-h-screen w-full overflow-hidden">
      {/* Fullscreen Background Image */}
      <Image
        src={SunriseBanner.src}
        alt="Sunrise Banner"
        fill
        priority
        className="object-cover w-full h-full absolute top-0 left-0 z-0"
      />
      {/* Overlay: Login Modal */}
      <div className="relative z-10 flex items-center min-h-screen">
        <div className="bg-black/50 backdrop-blur-md rounded-lg shadow-lg p-12 ml-16 max-w-lg w-full flex flex-col items-start">
          <div className="logo pb-8">
            <Image
              src={SunriseBlack.src}
              alt="Sunrise-Trade"
              width={250}
              height={350}
            />
          </div>
          <h2 className="text-2xl text-white">Sign in to your account</h2>
          <div className="w-full pt-8 max-w-md">
            <div>
              <div className="sm:hidden">
                <label htmlFor="tabs" className="sr-only">
                  Select a tab
                </label>
                <select
                  id="tabs"
                  name="tabs"
                  className="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500"
                >
                  {tabs.map((tab) => (
                    <option key={tab.name}>{tab.name}</option>
                  ))}
                </select>
              </div>
              <div className="hidden sm:block">
                <nav
                  className="isolate flex divide-x divide-gray-200 rounded-lg shadow"
                  aria-label="Tabs"
                >
                  {tabs.map((tab, tabIdx) => (
                    <a
                      key={tab.name}
                      href={tab.href}
                      className={classNames(
                        tab.current
                          ? "bg-gradient-to-r from-gray-700 to-gray-900 text-white shadow-lg scale-105"
                          : "text-[#222222] bg-white hover:bg-gray-200 hover:text-black",
                        tabIdx === 0 ? "rounded-l-lg" : "",
                        tabIdx === tabs.length - 1 ? "rounded-r-lg" : "",
                        "transition-all duration-150 cursor-pointer group relative min-w-0 flex-1 overflow-hidden py-4 px-4 text-center text-base font-semibold focus:z-10 border-none outline-none"
                      )}
                      aria-current={tab.current ? "page" : undefined}
                    >
                      <span>{tab.name}</span>
                      <span
                        aria-hidden="true"
                        className={classNames(
                          "absolute inset-x-0 bottom-0 h-0.5 bg-transparent"
                        )}
                      />
                    </a>
                  ))}
                </nav>
              </div>
            </div>
            <div className="pt-8">
              <LoginForm tabSelected={"organization"} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
