import React, { useState } from "react";
import Cookies from "js-cookie";
import config from "../../../config.json";

interface FormErrors {
  userName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  userGroup?: string;
}

export default function AddNewUserForm({ fetchData, setOpen, userGroupData }) {
  const [formFields, setFormFields] = useState({
    userName: "",
    email: "",
    password: "",
    confirmPassword: "",
    userGroup: userGroupData?.[0]?._id || "", // Auto-select first value
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [globalError, setGlobalError] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormFields({ ...formFields, [name]: value });
  };

  const validateForm = (): FormErrors => {
    let validationErrors: FormErrors = {};
    
    // Validate userName
    if (!formFields.userName.trim()) {
      validationErrors.userName = "User name is required.";
    }
    
    // Validate email
    if (!formFields.email) {
      validationErrors.email = "Email is required.";
    } else if (!/\S+@\S+\.\S+/.test(formFields.email)) {
      validationErrors.email = "Email address is invalid.";
    }
    
    // Validate password
    if (!formFields.password) {
      validationErrors.password = "Password is required.";
    } else if (formFields.password.length < 8) {
      validationErrors.password = "Password must be at least 8 characters.";
    }
    
    // Validate confirmPassword
    if (!formFields.confirmPassword) {
      validationErrors.confirmPassword = "Confirm password is required.";
    } else if (formFields.password !== formFields.confirmPassword) {
      validationErrors.confirmPassword = "Passwords do not match.";
    }
    
    // Validate userGroup
    if (!formFields.userGroup) {
      validationErrors.userGroup = "User role selection is required.";
    }
    
    return validationErrors;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const formData = {
        name: formFields.userName,
        username: formFields.email,
        password: formFields.password,
        userGroup: formFields.userGroup,
      };

      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
        body: JSON.stringify(formData),
      };

      const response = await fetch(`${config.BACKEND_URL}/user`, requestOptions);
      const data = await response.json();

      if (!data.error) {
        setOpen(false);
        fetchData();
      } else {
        setGlobalError(data.error);
      }
    } catch (error) {
      console.error("Failed to create user:", error);
      setGlobalError("An unexpected error occurred. Please try again later.");
    }
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <h2 className="text-center font-semibold leading-7 text-gray-900">
              Create User
            </h2>
          </div>

          {globalError && <p className="text-red-600 font-bold">{globalError}</p>}

          {/* User Name */}
          <div className="sm:col-span-3">
            <label htmlFor="userName" className="block text-sm font-medium leading-6 text-gray-900">
              User Name
            </label>
            <div className="mt-2">
              <input
                name="userName"
                id="userName"
                value={formFields.userName}
                onChange={handleInputChange}
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm"
              />
              {errors.userName && <p className="text-red-600">{errors.userName}</p>}
            </div>
          </div>

          {/* Email */}
          <div className="sm:col-span-3">
            <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900">
              Email
            </label>
            <div className="mt-2">
              <input
                name="email"
                id="email"
                value={formFields.email}
                onChange={handleInputChange}
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm"
              />
              {errors.email && <p className="text-red-600">{errors.email}</p>}
            </div>
          </div>

          {/* Password */}
          <div className="sm:col-span-3">
            <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900">
              Password
            </label>
            <div className="mt-2">
              <input
                type="password"
                name="password"
                id="password"
                value={formFields.password}
                onChange={handleInputChange}
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm"
              />
              {errors.password && <p className="text-red-600">{errors.password}</p>}
            </div>
          </div>

          {/* Confirm Password */}
          <div className="sm:col-span-3">
            <label htmlFor="confirmPassword" className="block text-sm font-medium leading-6 text-gray-900">
              Confirm Password
            </label>
            <div className="mt-2">
              <input
                type="password"
                name="confirmPassword"
                id="confirmPassword"
                value={formFields.confirmPassword}
                onChange={handleInputChange}
                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm"
              />
              {errors.confirmPassword && <p className="text-red-600">{errors.confirmPassword}</p>}
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="mt-6 flex items-center justify-center gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 w-[30%] text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
            onClick={() => setOpen(false)}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="rounded-md w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm"
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
}
