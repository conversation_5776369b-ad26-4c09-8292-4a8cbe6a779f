import { useMutation } from "@tanstack/react-query";
import React, { useState } from "react";
import config from "../../../config.json";
import Cookies from "js-cookie";

type FormData = {
  name: string;
  note: string;
};

export default function NewCompanyForm({ fetchData, setOpen }) {
  const [error, setError] = useState("");

  const newCompany = useMutation({
    mutationFn: async (newData: FormData) => {
      const requestOptions: RequestInit = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
        body: JSON.stringify(newData),
      };

      const response = await fetch(
        `${config.BACKEND_URL}/company`,
        requestOptions
      );
      const newResponse = await response.json();
      setError("");
      setOpen(false);
      fetchData();
      return await response.json();
    },
  });
  const [formData, setFormData] = useState<FormData>({
    name: "",
    note: "",
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({
      ...prev,
      [event.target.name]: event.target.value,
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    if (!formData.name.trim()) {
      setError("Please Fill Name field");
      return;
    }
    newCompany.mutate(formData);
  };

  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <h2 className="text-center font-semibold leading-7 text-gray-900">
              Add New company
            </h2>
          </div>

          <div className="pb-6">
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
              <div className="sm:col-span-3">
                <label
                  htmlFor="noteName"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Name
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="name"
                    id="noteName"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <label
                  htmlFor="note"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Note
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="note"
                    id="note"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {error && <p className="text-red-600 font-bold">{error}</p>}
        <div className="mt-6 flex items-center justify-center gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 w-[30%] text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
            onClick={() => setOpen(false)}
          >
            {" "}
            Cancel{" "}
          </button>
          <button
            type="submit"
            className="rounded-md  w-[30%] bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
}
