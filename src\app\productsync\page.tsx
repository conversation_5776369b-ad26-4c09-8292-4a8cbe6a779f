"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import axios from "axios";
import Cookies from "js-cookie";
import Sidebar from "../components/sidebar";
import ErrorNotification from "../components/ErrorNotification";
import { NotFound } from "../components/notFound";
import { ImSpinner8 } from "react-icons/im";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import { formattedDate } from "../helpers/dateHelpers";
import config from "../../../config.json";
import Pagination from "../components/pagination";
import SuccessNotification from "../components/SuccessNotifications";

interface FormattedTime {
  hour: string;
  min: string;
  period: string;
}

interface SyncData {
  startTime: FormattedTime;
  endTime: FormattedTime;
  startTimeMY:FormattedTime,
  endTimeMY:FormattedTime,
  triggeredBy: string;
  status: string;
  duration: number;
  totalProducts: number;
  syncedProducts: number;
  updatedAt: FormattedTime;
  createdAt: string;
}

interface ProductsSyncProps {
  logsData: SyncData[];
  fetching: boolean;
  page: number;
  handlePageChange: (newPage: number) => void;
}

interface RefreshButton {
  isClicked: boolean;
  handleRefreshButton: () => void;
}

const ProductSyncingDataTable = (props: ProductsSyncProps) => {
  const { logsData, fetching, page, handlePageChange } = props;
  return (
    <>
      <table className="table-fixed w-full rounded-lg shadow-sm text-center divide-y divide-gray-900 text-sm">
        <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg text-white">
          <tr>
            <th className="py-3 px-3">Date</th>
            <th className="py-3 px-3">Start time</th>
            <th className="py-3 px-3">End time</th>
            <th className="py-3 px-3">Triggerd by</th>
            <th className="py-3 px-3">Status</th>
            <th className="py-3 px-3">Duration (min)</th>
            <th className="py-3 px-3">Processed Sku</th>
            <th className="py-3 px-3">Total Sku</th>
            <th className="py-3 px-3">Last Sync</th>
          </tr>
        </thead>
        <tbody>
          {fetching ? (
            <tr>
              <td colSpan={9} className="py-6 text-center">
                <ImSpinner8
                  fill="black"
                  size={30}
                  className="inline-block animate-spin"
                />
              </td>
            </tr>
          ) : logsData.length === 0 ? (
            <NotFound />
          ) : (
            logsData.map((data: SyncData, index: any) => {
              const { startTime, endTime, updatedAt, status, createdAt } = data;

              let backgoundColor: string;
              switch (status) {
                case "RUNNING": {
                  backgoundColor = "bg-yellow-100 text-yellow-800";
                  break;
                }
                case "FAILED": {
                  backgoundColor = "bg-red-100 text-red-800";
                  break;
                }
                case "SUCCESS": {
                  backgoundColor = "bg-green-100 text-green-800";
                  break;
                }
                default:
                  backgoundColor = "none";
              }

              return (
                <tr key={index}>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {createdAt ? new Date(createdAt).toLocaleDateString() : "-"}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {`${startTime.hour}:${startTime.min} ${startTime.period}`}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {`${endTime.hour}:${endTime.min} ${endTime.period}`}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {data?.triggeredBy ?? "-"}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    <span className={`${backgoundColor} p-2 rounded-md`}>
                      {status || "-"}
                    </span>
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {data?.duration ?? "-"}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222] ">
                    {data?.totalProducts ?? "-"}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {data?.totalProducts ?? "-"}
                  </td>
                  <td className="py-3 px-3 border-b border-gray-300 text-[#222222]">
                    {`${updatedAt.hour}:${updatedAt.min} ${updatedAt.period}`}
                  </td>
                </tr>
              );
            })
          )}
        </tbody>
      </table>
      {page > 1 && (
        <Pagination paginationCount={page} onPageChange={handlePageChange} />
      )}
    </>
  );
};

const RefreshButton = (props: RefreshButton) => {
  const { handleRefreshButton, isClicked } = props;
  return (
    <button
      type="button"
      className="flex justify-around w-[5%] rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm transition transform active:scale-95  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
      onClick={handleRefreshButton}
    >
      <ArrowPathIcon
        className={`${isClicked ? "animate-spin" : "animate-none"} h-5 w-10`}
      />
    </button>
  );
};

function ProductSyncWrapper() {
  const limit = 50;
  const [fetching, setIsFetching] = useState(false);
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [page, setPage] = useState(1);
  const [error, setError] = useState("");
  const [logsData, setLogsData] = useState([]);
  const [buttonLoader, setButtonLoader] = useState(true);
  const [message, setMessage] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);

  const handlePageChange = useCallback((page: number) => {
    setPage(page);
    getAllLogs(page, limit);
  }, []);

  const handleRefreshButton = useCallback(() => {
    setRefreshButtonClicked(true);
    getAllLogs(page, limit);
  }, [refreshButtonClicked]);

  const productSyncManually = async () => {
    try {
      setButtonLoader(true);
      const response = await axios.post(`${config.BACKEND_URL}/cron`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("token")}`,
        },
      });
      if (response.data.status === "success") {
        setShowSuccess(true);
        setMessage(response.data?.data?.message);
      }
    } catch (error) {
      setButtonLoader(false);
      console.error("Error syncing products:", error);
    }
  };

  const getAllLogs = async (page = 1, limit = 50) => {
    setIsFetching(true);
    const url = `${config.BACKEND_URL}/cron/all?page=${page}&limit=${limit}`;
    try {
      const response = await axios.get(url);
      // console.log("resp",response.data)
      if (!response.data.responseCode) {
        const totalCount = response.data.data.totalCount;
        const totalPages = Math.ceil(totalCount / limit);
        setPage(totalPages);
        setLogsData(response.data.data.data);
      } else {
        const errors = response.data.errors
          .map((error: any) => error.message)
          .join(",");

        setError(errors);
      }
      setIsFetching(false);
    } catch (err) {
      console.log("Error occurred in fetching the logs", err);
      setIsFetching(false);
      setError("something went wrong");
    } finally {
      setIsFetching(false);
      setRefreshButtonClicked(false);
    }
  };

  useEffect(() => {
    let intervalId;

    const pollCronStatus = async () => {
      const response = await axios.get(`${config.BACKEND_URL}/cron`, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const loading = response?.data?.data?.loading;
      setButtonLoader(loading);

      // Stop polling if it's no longer loading
      if (!loading) {
        clearInterval(intervalId);
      }
    };

    pollCronStatus();
    intervalId = setInterval(pollCronStatus, 60000);

    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    getAllLogs(page, limit);
  }, []);

  const formattedLogData: any[] = useMemo(() => {
    return logsData.map((data: SyncData) => {
      const {
        startTime = null,
        endTime = null,
        updatedAt = null,
        status = "",
        triggeredBy = "",
        duration = null,
        createdAt = "",
        totalProducts = 0,
        startTimeMY=null,
        endTimeMY=null,
      } = data;

      const formattedStartTime = formattedDate(startTimeMY);
      const formattedEndTime = formattedDate(endTimeMY);
      const formattedUpdatedAt = formattedDate(updatedAt);

      const formattedData = {
        ...data,
        status: status?.toUpperCase(),
        triggeredBy: triggeredBy?.toUpperCase(),
        startTime: formattedStartTime,
        endTime: formattedEndTime,
        updatedAt: formattedUpdatedAt,
        duration: duration && Math.ceil(duration / 60000),
        createdAt: createdAt ? new Date(createdAt).toLocaleDateString() : "-",
        totalProducts: totalProducts,
      };

      return formattedData;
    });
  }, [logsData]);

  return (
    <Sidebar>
      {showSuccess && <SuccessNotification message={message} />}
      <div className="inline-flex items-center justify-end w-full mt-4 mb-4 gap-2">
        <RefreshButton
          handleRefreshButton={handleRefreshButton}
          isClicked={refreshButtonClicked}
        />
        {buttonLoader ? (
          <button
            disabled
            type="button"
            className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
          >
            <svg
              aria-hidden="true"
              role="status"
              className="inline mr-3 w-4 h-4 text-white animate-spin"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="#E5E7EB"
              ></path>
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentColor"
              ></path>
            </svg>
            Cron running...
          </button>
        ) : (
          <button
            onClick={productSyncManually}
            type="button"
            className="rounded-md bg-[#222222] px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
          >
            Sync Products Manually
          </button>
        )}
      </div>

      <div className="min-h-screen bg-gray-50 py-8">
        {error && <ErrorNotification message={error} />}
        <div className="p-4">
          <ProductSyncingDataTable
            logsData={formattedLogData}
            fetching={fetching}
            page={page}
            handlePageChange={handlePageChange}
          />
        </div>
      </div>
    </Sidebar>
  );
}

export default ProductSyncWrapper;
