"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import { loginUser, loginUserDepartment } from "../utils/auth";
import Link from "next/link";

export default function LoginForm({ tabSelected }) {
  const [username, setUsername] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>();
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const router = useRouter();

  const handleLogin = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setLoading(true);
    try {
      let loginResponse;

      if (tabSelected == "organization") {
        loginResponse = await loginUser({ username, password });
      } else if (tabSelected == "department") {
        loginResponse = await loginUserDepartment({ username, password });
      }
      // if (!loginResponse?.token) return;
      if (loginResponse.token) {
        if (loginResponse.user.type == "admin_user") {
          Cookies.set("type", JSON.stringify(loginResponse.user.access_scopes));
        }
        Cookies.set("name", loginResponse.user.name);
        Cookies.set("userID", loginResponse.user._id);
        if (loginResponse.user.type !== "admin_user") {
          Cookies.set(
            "userrole",
            loginResponse.user?.departmentType[0]?.department
          );
        } else {
          Cookies.set("userrole", loginResponse.user.type);
        }
        Cookies.set("token", loginResponse.token, { expires: 1 / 3 });
        setTimeout(() => {
          router.push("/");
        }, 500);
      }
    } catch (err: any) {
      const errorMessage = err?.message || "An error occurred";
      setError(errorMessage);
        setLoading(false);
    }
  };
  return (
    <form onSubmit={handleLogin}>
      {error && (
        <div>
          <p className="text-red-600 text-center">{error}</p>
        </div>
      )}
      <label
        htmlFor="email"
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        Email Address
      </label>
      <input
        id="email"
        type="email"
        required
        value={username}
        onChange={(e) => setUsername(e.target.value)}
        className="block w-full rounded-md border-0 py-[0.7rem] text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
      />
      <label
        htmlFor="password"
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        Password
      </label>
      <div className="relative">
        <input
          id="password"
          type={showPassword ? "text" : "password"}
          required
          value={password}
          className="block w-full rounded-md border-0 py-[0.7rem] text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          onChange={(e) => setPassword(e.target.value)}
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute inset-y-0 right-0 pr-3 flex items-center"
        >
          {showPassword ? (
            // Eye Off Icon
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5 text-[#222222]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88"
              />
            </svg>
          ) : (
            // Eye Icon
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5 text-[#222222]"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          )}
        </button>
      </div>
      <div className="flex items-center justify-end">
        <Link
          href="/reset-password"
          className="text-sm text-gray-700 hover:text-indigo-500"
        >
          Forgot password?
        </Link>
      </div>

      {/* {error && (
        <div className="pt-8">
          <p className="text-red-600">{error.error}</p>
        </div>
      )} */}

      <div className="text-center">
        <button
          type="submit"
          disabled={loading}
          className="mt-8 w-full rounded-md bg-gray-700 px-2.5 py-[1rem] text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 flex items-center justify-center"
        >
        {loading ? (
  <svg
    className="animate-spin h-5 w-5 text-black"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="none"
      stroke="currentColor"
      strokeWidth="4"
      strokeLinecap="round"
      d="M22 12a10 10 0 00-10-10"
    />
  </svg>
) : (
  "Sign in"
)}

        </button>
      </div>
    </form>
  );
}
