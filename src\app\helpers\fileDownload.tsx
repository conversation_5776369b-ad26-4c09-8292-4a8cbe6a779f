// utils/downloadBase64File.ts

type MimeTypesMap = {
  [extension: string]: string;
};

const mimeTypes: MimeTypesMap = {
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  csv: "text/csv",
  pdf: "application/pdf",
  txt: "text/plain",
  jpg: "image/jpeg",
  png: "image/png",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  json: "application/json",
};

export const downloadBase64File = (
  base64: string,
  fileName: string,
  mimeType?: string
) => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "txt";
  const finalMimeType =
    mimeType || mimeTypes[extension] || "application/octet-stream";

  const blob = new Blob([Buffer.from(base64, "base64")], {
    type: finalMimeType,
  });
  const url = window.URL.createObjectURL(blob);

  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
