"use client";
import React, { useEffect, useState, Fragment, useRef } from "react";
import Sidebar from "../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../../config.json";
import {
  ChevronDownIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { DeleteConfirmationModal } from "@/app/components/deleteConfirmationModal";
import UpdatePeopleModal from "@/app/components/updatePeopleModal";
import Cookies from "js-cookie";
import ErrorModal from "@/app/components/errorModal";
import CommentModal from "@/app/components/CommentModal";
import SuccessNotification from "@/app/components/SuccessNotifications";
import ErrorNotification from "@/app/components/ErrorNotification";
import UploadSheetModal from "@/app/components/UploadSheetModal";
import { toast, ToastContainer } from "react-toastify";

type Department = {
  id: string;
  pseudoId: string;
  name: string;
};
type salesPerson = {
  _id: string;
  name: string;
};

type People = {
  id: string;
  name: string;
  email: string;
  departmentType: string;
};

const queryClient = new QueryClient();
export default function People() {
  const [PeopleTable, setPeopleTable] = useState<People[]>([]);
  const [departmentList, setDepartmentList] = useState<Department[]>([]);
  const [open, setOpen] = useState(false);
  const [updatePeopleModalOpen, setUpdatePeopleModalOpen] = useState(false);
  const [error, setError] = useState("");
  const [editId, setEditId] = useState();
  const cancelButtonRef = useRef(null);

  const [selectedPseudoId, setSelectedPseudoId] = useState("");
  const [errorModal, setErrorModal] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [notification, setNotification] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const [salesPerson, setSalesPerson] = useState<salesPerson[]>([]);
  const [selectedSalesPerson, setSelectedSalesPerson] = useState<any[]>([]);

  const dropdownRef = useRef<HTMLDivElement>(null);

  const [currentPerson, setCurrentPerson] = useState({
    name: "",
    email: "",
    department: "",
  });

  // Toggle dropdown
  const toggleDropdown = () => setIsOpen((prev) => !prev);

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Handle selection
  const handleCheckboxChange = (id: string) => {
    setSelectedSalesPerson((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((item) => item !== id)
        : [...prevSelected, id]
    );
  };

  const getSalesPersons = async () => {
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/department_people/all/salesperson`
      );
      console.log(response, "sales person");
      setSalesPerson(response.data?.data?.salesPersons);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getSalesPersons();
  }, []);

  const scopes = Cookies.get("type");
  const newScopes = scopes && JSON.parse(scopes);

  async function getDepartmentList() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/department_type`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        name: x.department,
        id: x._id,
        pseudoId: x.pseudoId,
      };
    });
    setDepartmentList(data);
  }

  const validateEmail = (email) => {
    return String(email)
      .toLowerCase()
      .match(/^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/);
  };

  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  async function deletePeople(id: any) {
    // await axios.request({
    //   method: "delete",
    //   url: `${config.BACKEND_URL}/department_people/${id}`,
    //   headers: {
    //     "Content-Type": "application/json",
    //   },
    // });

    const person = PeopleTable.find((p: any) => p.id === id);
    setCurrentPerson({
      name: person?.name || "",
      email: person?.email || "",
      department: person?.departmentType || "",
    });
    setCurrentId(id);
    setCurrentType("department_people");
    setConfirmationOpen(true);
  }

  function editPeople(id: any) {
    setEditId(id);
    setUpdatePeopleModalOpen(true);
  }

  const checkInERP = async (name, salespersonkey) => {
    // console.log(name, salespersonkey);
    try {
      const response = await axios.get(
        `${config.BACKEND_URL}/ERPSync/salesPersons`
      );
      const salesPersons = response.data.data.salespersons || [];

      // console.log(salesPersons);

      // Check for both key and name match
      const bothMatch = salesPersons.find(
        (x) =>
          x.SalespersonKey.toLowerCase() === salespersonkey.toLowerCase() &&
          x.Name.toLowerCase() === name.toLowerCase()
      );

      if (bothMatch) {
        return {
          message: "Both key and name match",
          errorModal: true,
          exist: true,
          data: bothMatch,
        };
      }

      const nameMatch = salesPersons.find(
        (x) =>
          x.SalespersonKey.toLowerCase() !== salespersonkey.toLowerCase() &&
          x.Name.toLowerCase() === name.toLowerCase()
      );

      if (nameMatch) {
        return {
          message: "Sales person name already exist in the ERP",
          errorModal: true,
          exist: true,
        };
      }

      // Check for only key match
      const keyMatch = salesPersons.find(
        (x) =>
          x.SalespersonKey.toLowerCase() === salespersonkey.toLowerCase() &&
          x.Name.toLowerCase() !== name.toLowerCase()
      );

      if (keyMatch) {
        return {
          message: "Sales person key already exist in the ERP",
          confirmModal: true,
          exist: true,
        };
      }

      // If no match
      return {
        message: "Both key and name missing",
        showModal: false,
        exist: false,
      };
    } catch (error) {
      console.error("Error fetching salespersons:", error);
      throw error; // Re-throw the error for further handling if needed
    }
  };

  const checkInDB = async (key) => {
    const data = await axios.get(
      `${config.BACKEND_URL}/department_people/salesperson/${key}`
    );
    // console.log(data.data, "data in db");
    return data.data.data;
  };

  async function fetchPeopleTable() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/department_people`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        id: x?._id,
        name: x?.name,
        email: x?.email,
        designation: x?.designation?.name,
        departmentType: x?.departmentType[0]?.department,
        reporting: x?.reportingTo,
        distributor: x?.distributor,
        salesPersonKey: x?.salespersonkey,
      };
    });
    setPeopleTable(data);
    return data;
  }

  const saveDataInERP = async (name, salespersonkey) => {
    try {
      const response = await fetch(
        `${config.BACKEND_URL}/ERPSync/salesPersons`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            salesPersonName: name,
            salesPersonKey: salespersonkey,
          }),
        }
      );
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error:", errorData);
        setError(errorData.message || "Something went wrong.");
        return;
      }

      const responseData = await response.json();
      // console.log("Success:", responseData);
      return responseData;
    } catch (error) {
      console.error("Error:", error);
      setError("Failed to create person. Please try again.");
    }
  };

  const saveDataInDB = async () => {
    try {
      const name = (
        document.getElementById("name") as HTMLInputElement
      ).value.trim();
      const email = (
        document.getElementById("email") as HTMLInputElement
      ).value.trim();
      const departmentType = (
        document.getElementById("department") as HTMLSelectElement
      ).value.trim();
      const password = (
        document.getElementById("password") as HTMLInputElement
      ).value.trim();
      const salespersonkey = (
        document.getElementById("salespersonkey") as HTMLInputElement
      )?.value
        .trim()
        .toUpperCase();

      const dataInDB = await checkInDB(salespersonkey);
      if (dataInDB.length > 0) {
        setErrorModal(true);
        setTimeout(() => {
          setErrorModal(false);
          setNotification(false);
        }, 5000);
        setMessage("Data already exist in databse");
        setOpen(!open);
        return;
      }
      if (
        selectedPseudoId === "SUNRISE_SALES_COORDINATOR" &&
        selectedSalesPerson.length === 0
      ) {
        setError("Please select at least one Sales Person.");
        return;
      }

      const response = await fetch(`${config.BACKEND_URL}/department_people`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          email,
          departmentType,
          password,
          salespersonkey,
          salesPersons: selectedSalesPerson,
        }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        console.error("Error:", errorData);
        setError(errorData.message || "Something went wrong.");
        return;
      }
      const responseData = await response.json();
      // console.log("Success:", responseData);
      setNotification(true);
      setMessage("Data saved successfully");
      setTimeout(() => {
        setErrorModal(false);
        setNotification(false);
      }, 5000);
      fetchPeopleTable();
      setOpen(false);
    } catch (error) {
      console.error("Error:", error);
      setErrorModal(true);
      setTimeout(() => {
        setErrorModal(false);
        setNotification(false);
      }, 5000);
      setMessage("Failed to create person. Please try again.");
      setError("Failed to create person. Please try again.");
    }
  };

  async function sendPeopleCreationData() {
    try {
      const name = (
        document.getElementById("name") as HTMLInputElement
      ).value.trim();
      const email = (
        document.getElementById("email") as HTMLInputElement
      ).value.trim();
      const departmentType = (
        document.getElementById("department") as HTMLSelectElement
      ).value.trim();
      const password = (
        document.getElementById("password") as HTMLInputElement
      ).value.trim();
      const salespersonkey = (
        document.getElementById("salespersonkey") as HTMLInputElement
      )?.value
        .trim()
        .toUpperCase();

      // Basic validation for all fields
      if (
        !name ||
        !email ||
        !departmentType ||
        !password ||
        (selectedPseudoId === "SALES_PERSON" && !salespersonkey)
      ) {
        setError(
          selectedPseudoId === "SALES_PERSON"
            ? "Please fill all fields, including the key for Sales Person."
            : "Please fill all fields."
        );
        return;
      }
      if (
        selectedPseudoId === "SUNRISE_COORDINATOR" &&
        selectedSalesPerson.length < 0
      ) {
        setError("Please select at least one Sales Person.");
        return;
      }

      // Validate email format
      if (!validateEmail(email)) {
        setError("Please provide a valid email address.");
        return;
      }

      if (selectedPseudoId === "SALES_PERSON") {
        const erpCheck = await checkInERP(name, salespersonkey);
        // console.log(erpCheck);
        if (!erpCheck.exist) {
          try {
            const data = await saveDataInERP(name, salespersonkey);
            const dataInDB = await checkInDB(salespersonkey);
            if (dataInDB.length > 0) {
              setErrorModal(true);
              setMessage("Already exist in the database");
              setTimeout(() => {
                setErrorModal(false);
                setNotification(false);
              }, 5000);
              return;
            }
            if (data.status === "success" && data.responseCode === 0) {
              const response = await fetch(
                `${config.BACKEND_URL}/department_people`,
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                  },
                  body: JSON.stringify({
                    name,
                    email,
                    departmentType,
                    password,
                    salespersonkey,
                  }),
                }
              );
              if (!response.ok) {
                const errorData = await response.json();
                console.error("Error:", errorData);
                setError(errorData.message || "Something went wrong.");
                return;
              }
              const responseData = await response.json();
              // console.log("Success:", responseData);
              setNotification(true);
              setMessage("Data saved successfully");
              setTimeout(() => {
                setErrorModal(false);
                setNotification(false);
              }, 5000);
              fetchPeopleTable();
              setOpen(false);
            }
          } catch (error) {
            console.error("Error:", error);
            setError("Failed to create person. Please try again.");
          }
        } else if (erpCheck.exist && erpCheck.errorModal) {
          const dataInDB = await checkInDB(salespersonkey);
          if (dataInDB.length > 0) {
            setErrorModal(true);
            setMessage("Data already exist in both ERP and databse");
            setTimeout(() => {
              setErrorModal(false);
              setNotification(false);
            }, 5000);
            setOpen(!open);
            return;
          }
          await saveDataInDB();
          setNotification(true);
          setMessage("Data Saved successfully");
          setTimeout(() => {
            setErrorModal(false);
            setNotification(false);
          }, 5000);
          setOpen(!open);
        } else if (erpCheck.exist && erpCheck.confirmModal) {
          setConfirmModal(true);
          setOpen(!open);
          setTitle("Name Matched");
          setMessage(
            "The name is different, but the key (Code) already exist in the ERP. Please click Confirm to save the data in Database or Cancel to change the name"
          );
        }
      } else {
        await saveDataInDB();
      }
    } catch (error) {
      console.log(error);
    }
  }

  const handleFileUpload = async (file: File) => {
    setModalLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await axios.post(
        `${config.BACKEND_URL}/department_people/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      console.log(response.data);

      if (response.data.status === "success") {
        setNotification(true);
        if (response.data.data.errorCount > 0) {
          // Handle error file download
          const base64File = response.data.data.errorFile;
          const binaryString = atob(base64File);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          const blob = new Blob([bytes], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = "error_records.xlsx";
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);

          setMessage(
            `Upload completed. ${response.data.data.successCount} records processed successfully. ${response.data.data.errorCount} records had errors. Error file downloaded.`
          );
        } else {
          setMessage(
            `All ${response.data.data.successCount} records processed successfully!`
          );
        }
        fetchPeopleTable(); // Refresh the table data
      } else {
        setErrorModal(true);
        setMessage(response.data.errors?.[0]?.message || "Upload failed");
      }

      setIsModalOpen(false);
    } catch (error) {
      console.error("Upload failed:", error);
      setErrorModal(true);
      setMessage("Failed to upload file. Please try again.");
    } finally {
      setModalLoading(false);
      // Reset feedback states after completion
      setErrorModal(false);
      setNotification(false);
    }
  };

  useEffect(() => {
    fetchPeopleTable();
    getDepartmentList();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            className="relative z-10"
            initialFocus={cancelButtonRef}
            onClose={setOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>
            <div className="fixed inset-0 z-10 w-screen overflow-y-scroll pt-12">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <Dialog.Panel className="relative transform overflow-auto max-h-[75vh] rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div className="flex justify-end">
                      <XMarkIcon
                        onClick={() => setOpen(false)}
                        className="cursor-pointer h-6 w-6 text-black"
                      />
                    </div>
                    <div>
                      <div className="mt-3 text-center sm:mt-5">
                        <div className="isolate -space-y-px rounded-md shadow-sm">
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Name
                            </label>
                            <input
                              type="text"
                              name="name"
                              id="name"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="email"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Email
                            </label>
                            <input
                              type="email"
                              name="email"
                              id="email"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="reportingTo"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Password
                            </label>
                            <input
                              type="password"
                              name="password"
                              id="password"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                          <div className="relative">
                            <label
                              htmlFor="department"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Department
                            </label>
                            <select
                              id="department"
                              name="department"
                              onChange={(e) => {
                                const selectedDepartment = departmentList.find(
                                  (dept: {
                                    id: string;
                                    pseudoId: string;
                                    name: string;
                                  }) => dept.id === e.target.value
                                );
                                setSelectedPseudoId(
                                  selectedDepartment?.pseudoId || ""
                                );
                              }}
                              className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              defaultValue="Canada"
                            >
                              <option value={""}>Select Department</option>
                              {departmentList.map((x: any) => (
                                <option
                                  className="text-black"
                                  value={x.id}
                                  key={x.id}
                                >
                                  {x.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          <br></br>
                          {selectedPseudoId === "SALES_PERSON" && (
                            <div className="relative">
                              <label
                                htmlFor="salespersonkey"
                                className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                              >
                                Key
                              </label>
                              <input
                                type="text"
                                name="salespersonkey"
                                id="salespersonkey"
                                className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                              />
                            </div>
                          )}
                          <div className="relative w-full">
                            {selectedPseudoId ===
                              "SUNRISE_SALES_COORDINATOR" && (
                              <div
                                className="relative w-full"
                                ref={dropdownRef}
                              >
                                <div
                                  onClick={toggleDropdown}
                                  className="cursor-pointer w-full border border-gray-300 rounded-md px-3 py-2 bg-white text-sm text-gray-900"
                                >
                                  {selectedSalesPerson.length > 0
                                    ? salesPerson
                                        .filter((dept) =>
                                          selectedSalesPerson.includes(dept._id)
                                        )
                                        .map((dept) => dept.name)
                                        .join(", ")
                                    : "Select Sales Person"}
                                </div>

                                {isOpen && (
                                  <div className="z-20 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-300 bg-white shadow-lg">
                                    {salesPerson.map((dept) => (
                                      <label
                                        key={dept._id}
                                        className="flex items-center px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer text-black"
                                      >
                                        <input
                                          type="checkbox"
                                          className="mr-2"
                                          checked={selectedSalesPerson.includes(
                                            dept._id
                                          )}
                                          onChange={() =>
                                            handleCheckboxChange(dept._id)
                                          }
                                        />
                                        {dept.name}
                                      </label>
                                    ))}
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 sm:mt-6 sm:gap-3">
                      {error && (
                        <p className="text-red-600 font-bold">{error}</p>
                      )}
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 sm:col-start-2"
                        onClick={() => {
                          sendPeopleCreationData();
                          // setOpen(false);
                        }}
                      >
                        Create
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>
        {errorModal && <ErrorNotification message={message} />}
        {notification && <SuccessNotification message={message} />}
        {confirmModal && (
          <CommentModal
            title={title || "Name Matched"}
            message={
              message ||
              "The name is different, but the key (Code) already exists in the ERP. Please click Confirm to save the data in Database or Cancel to change the name."
            }
            onClose={() => setConfirmModal(!confirmModal)}
            onConfirm={() => saveDataInDB()}
          />
        )}
        <div className="px-4 pt-4 sm:px-5 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto"></div>
            <div className="mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
              <button
                onClick={() => setOpen(true)}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Add User
              </button>
            </div>
            <div className="mt-4 sm:ml-5 sm:mt-0 sm:flex-none">
              <button
                onClick={() => setIsModalOpen(true)}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Add Salesperson in Bulk
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                  <thead className="bg-[#222222] rounded-tl-lg rounded-tr-lg">
                    <tr>
                      <th
                        scope="col"
                        className="rounded-tl-lg px-3 py-3.5 text-left text-sm font-semibold text-white"
                      >
                        Name
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                      >
                        Email
                      </th>
                      <th
                        scope="col"
                        className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                      >
                        Department
                      </th>

                      {newScopes && newScopes?.people?.length > 0 && (
                        <>
                          {newScopes?.people.includes("write") && (
                            <th
                              scope="col"
                              className="px-3 py-3.5 text-left text-sm font-semibold text-white"
                            >
                              Delete
                            </th>
                          )}
                          {newScopes?.people.includes("delete") && (
                            <th
                              scope="col"
                              className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-white"
                            >
                              Edit
                            </th>
                          )}
                        </>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {PeopleTable.map((x: any) => (
                      <tr key={x.id} className="even:bg-gray-50">
                        <td
                          className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3 cursor-pointer"
                          onClick={() => {
                            editPeople(x.id);
                          }}
                        >
                          {x.name}
                        </td>
                        <td
                          className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3 cursor-pointer"
                          onClick={() => {
                            editPeople(x.id);
                          }}
                        >
                          {x.email}
                        </td>

                        <td
                          className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3 cursor-pointer"
                          onClick={() => {
                            editPeople(x.id);
                          }}
                        >
                          {x.departmentType}
                        </td>

                        {/* <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                          {x.distributor.map((value, index) => {
                            return `${value.firstName + " " + value.lastName}${
                              index != x.distributor.length - 1 ? ", " : ""
                            }`;
                          })}
                        </td> */}
                        {newScopes && newScopes?.people?.length > 0 && (
                          <>
                            {newScopes?.people.includes("delete") && (
                              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                                <button
                                  onClick={() => {
                                    deletePeople(x.id);
                                  }}
                                  type="button"
                                  className="block rounded-full bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                                >
                                  <TrashIcon
                                    className="h-5 w-5"
                                    aria-hidden="true"
                                  />
                                </button>
                              </td>
                            )}
                            {newScopes?.people.includes("write") && (
                              <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                                <button
                                  onClick={() => {
                                    editPeople(x.id);
                                  }}
                                  type="button"
                                  className="block rounded-full bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
                                >
                                  <PencilIcon
                                    className="h-5 w-5"
                                    aria-hidden="true"
                                  />
                                </button>
                              </td>
                            )}
                          </>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchPeopleTable}
            setTableData={setPeopleTable}
            personName={currentPerson.name}
            personEmail={currentPerson.email}
            personDepartment={currentPerson.department}
          />
        )}
        <UpdatePeopleModal
          open={updatePeopleModalOpen}
          setOpen={setUpdatePeopleModalOpen}
          editId={editId}
          fetchData={fetchPeopleTable}
          departmentList={departmentList}
        />

        <UploadSheetModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleFileUpload}
          sampleFileUrl=""
          title="Upload Sheet"
          acceptedFileTypes=".xlsx,.xls,.csv"
          fullStockSheetUrl=""
          loading={modalLoading}
        />
      </Sidebar>
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </QueryClientProvider>
  );
}
