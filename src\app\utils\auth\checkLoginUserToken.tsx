import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Cookies from 'js-cookie';

export default function useCheckLoginUserToken() {
  console.log("Hello 2")
  const router = useRouter();
  
  useEffect(() => {
    const getToken = () => {
      try {
        console.log("Hello 3")
        const token = Cookies.get('token');
        if (!token) {
          router.push('/login');
        }
  
      } catch (error) {
        console.error("Error getting token:");
        router.push('/login');
      }
    };

    getToken(); 
  }, [router]);  

}
