"use client"

import { Fragment, useEffect, useRef, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { CheckIcon } from '@heroicons/react/24/outline'
import { useMutation, useQuery } from '@tanstack/react-query';
// import { sendEmail } from '../orders/shipment/[id]/page';
import StatusDropdown from './statusDropdown';
import Cookies from "js-cookie";

export default function Modal({open, setOpen, openModalOnClick, page,  modalType, data, formValues, fields, refetch}) {
  console.log(page, " pageeee modal")
  console.log(fields, " fieldsssss")
  console.log(data, " data data data")
  console.log(" ===> ",open, modalType, data, formValues, fields, " =============================== ")

  const cancelButtonRef = useRef(null);
  const [formData, setFormData] = useState(data || []);

  const [addFormData, setaddFormData] = useState(formValues);

  const handleAddDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setaddFormData(prevData => ({
      ...prevData,
      [name]: value,
    }));
  };

  
  console.log(data, " dataaaaaa")
  
  const handleChange = (index, key, value) => {
    const newData = [...formData];
    newData[index][key] = value;
    setFormData(newData);
  };

  const handleShipmentModalChange = (statusId, status) => {
    const newData = [...formData];
    console.log(status, " tttttttttttttttttttttttttttttttttt ", statusId)
    newData[0]["statusId"] = statusId;
    newData[0]["status"] = status;
    newData[0]["isStatusChanged"] = true;
    setFormData(newData);
  };

  console.log(formData, " formDataformDataformDataformData")
  
  console.log(addFormData, " addFormDataaddFormDataaddFormData")
  const saveData = async () => {
    const newData = await addNewDistributor(addFormData);

    console.log(newData, " new dataaaaaa")
    // setFormData(prevData => [...prevData, newData]);
  }


    const updateDistributorData = useMutation({
      mutationFn: async (formData: any) => {
        const requestOptions: RequestInit = {
          method: 'PATCH', 
          headers: {
            'Content-Type': 'application/json', 
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          body: JSON.stringify(formData[0]), 
        };

        const createDistributorPromise = await fetch(`https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/${page}/${formData[0]._id}`, requestOptions);
        openModalOnClick();
        refetch();

        if(page == "shipment"){
          const order = {
              "email": formData[0].attributes[0].value[0],
              "name": "User",
              "orderId": formData[0].orderId,
              "orderStatus": formData[0].status
          };
          // sendEmail(order);
        }
        return createDistributorPromise.json();
      },
    });
    
    const addDistributorData = useMutation({
      mutationFn: async (addFormData: any) => {
        const requestOptions: RequestInit = {
          method: 'POST', 
          headers: {
            'Content-Type': 'application/json', 
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          body: JSON.stringify(addFormData), 
        };

        const createDistributorPromise = await fetch(`https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/${page}`, requestOptions);
        openModalOnClick();
        refetch();
        return createDistributorPromise.json();
      },
    });

    

    console.log(page == "shipment", " sssssssssssssssssssssssssssssssssssssssssssssssssssss")


    console.log(formData, " form dataaaaaaaaa")

    

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog className="relative z-10" initialFocus={cancelButtonRef} onClose={setOpen}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-x-auto h-96 rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  {/* {modalType == "edit" &&
                  <form>
                    {formData?.map((item, index) => (
                      <div key={index}>
                        <label htmlFor='name'>
                          Name
                          <input
                            id='name'
                            type="text"
                            value={item.name}
                            className="block w-full px-2 mb-4 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(e) => handleChange(index, 'name', e.target.value)}
                          />
                        </label>

                        <label htmlFor='shopifyCompanyId'>
                          Shopify Company Id
                          <input
                            id='shopifyCompanyId'
                            type="text"
                            value={item.shopifyCompanyId}
                            className="block w-full rounded-md px-2 mb-4 border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(e) => handleChange(index, 'shopifyCompanyId', e.target.value)}
                          />
                        </label>

                        <label htmlFor='email'>
                          Email
                          <input
                            type="text"
                            value={item.email}
                            className="block w-full rounded-md px-2 mb-4 border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(e) => handleChange(index, 'email', e.target.value)}
                          />
                        </label>

                        <label htmlFor='priority'>
                          Priority
                          <input
                            type="number"
                            value={item.priority}
                            className="block w-full rounded-md px-2 mb-4 border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(e) => handleChange(index, 'priority', parseInt(e.target.value))}
                          />
                        </label>

                        <label htmlFor='internalCode'>
                          Internal Code
                          <input
                            type="text"
                            value={item.internalCode}
                            className="block w-full rounded-md px-2 mb-4 border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            onChange={(e) => handleChange(index, 'internalCode', e.target.value)}
                          />
                        </label>
                      </div>
                    ))}
                    <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                        onClick={() => updateDistributorData.mutate(formData)}
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                        onClick={() => openModalOnClick()}
                        ref={cancelButtonRef}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                } */}

                {/* {modalType == "add" && 
                  <form>
                    {fields.map((field) => (
                      <label key={field.name} htmlFor={field.name}>
                        {field.label}
                        <input
                          onChange={(e) => handleAddDataChange(e)}
                          name={field.name}
                          id={field.name}
                          required
                          type={field.type}
                          className="block w-full px-2 mb-4 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </label>
                    ))}
                    <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                        onClick={() => addDistributorData.mutate(addFormData)}
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                        onClick={() => openModalOnClick()}
                        ref={cancelButtonRef}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                } */}


                {modalType == "editShipment" &&
                    <form>
                    {fields.map((field) => (
                      <label key={field.name} htmlFor={field.name}>
                        {field.label}
                        <input
                          onChange={(e) => handleAddDataChange(e)}
                          name={field.name}
                          id={field.name}
                          required
                          type={field.type}
                          className="block w-full px-2 mb-4 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </label>
                    ))}
                    <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                        onClick={() => addDistributorData.mutate(addFormData)}
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                        onClick={() => openModalOnClick()}
                        ref={cancelButtonRef}
                      >
                        Cancel
                      </button>
                    </div>
                    </form>
                }


               
                {page == "attributes" &&
                    <form>
                    {fields.map((field: any) => (
                      <label key={field.name} htmlFor={field.name}>
                        {field.label}
                        <input
                          onChange={(e) => handleAddDataChange(e)}
                          name={field.name}
                          id={field.name}
                          required
                          type={field.type}
                          className="block w-full px-2 mb-4 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </label>
                    ))}
                    <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                        onClick={() => addDistributorData.mutate(addFormData)}
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                        onClick={() => openModalOnClick()}
                        ref={cancelButtonRef}
                      >
                        Cancel
                      </button>
                    </div>
                    </form>
                }


                {page == "shipment" && 
                
                <form>
                {formData?.map((item, index) => (
                  <div key={index}>
                    <div>
                      <label>
                        ID: {item._id}
                        <input type="hidden" value={item?._id} name='shipmentId' />
                      </label>
                    </div>
                    <div>
                      <label>
                        Items: {item.items}
                      </label>
                    </div>
                    <div className='mb-4'>
                      <label>
                        Amount: {item.amount}
                      </label>
                    </div>
                   
                      
                    
                  </div>
                ))}
                <StatusDropdown handleChange={handleShipmentModalChange} />
                <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                    onClick={() => updateDistributorData.mutate(formData)}
                  >
                    Save
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                    onClick={() => openModalOnClick()}
                    ref={cancelButtonRef}
                  >
                    Cancel
                  </button>
                </div>
              </form>

                }

                </div>
                
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}


export async function addNewDistributor(formData: Object){
  const myHeaders = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${Cookies.get("token")}`,
  }

  const raw = JSON.stringify(formData);

  const requestOptions: RequestInit = {
    method: "POST",
    headers: myHeaders,
    body: raw,
    redirect: "follow"
  };

  const createDistributorPromise = await fetch("http://localhost:3600/api/distributor", requestOptions);
  const createDistributor = await createDistributorPromise.json();

  return createDistributor;
}