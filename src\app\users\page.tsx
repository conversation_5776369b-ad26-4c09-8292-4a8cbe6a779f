"use client";

import React, { useCallback, useEffect, useState } from 'react'
import { QueryClientProvider, QueryClient, useQuery } from "@tanstack/react-query";
import Sidebar from '@/app/components/sidebar';
import UsersModal from '@/app/components/usersModal';
import { PencilIcon, TrashIcon } from '@heroicons/react/20/solid'
import Cookies from 'js-cookie';
// import { GetServerSideProps } from 'next';
import config from "../../../config.json";

const queryClient = new QueryClient();

export default function Users() {
  const [open, setOpen] = useState(false);
  const [edit, setEdit] = useState(false);
  const [addNewUser, setAddNewUser] = useState(false);
  const [newScopes, setNewScopes]: any = useState({});


  useEffect(() => {
    const scopes = Cookies.get("type");
    const getScopes = scopes && JSON.parse(scopes);
    setNewScopes(getScopes);
  },[])

  const [data, setData] = useState<any[]>([]);

  const fetchData = useCallback(() => {
    const requestOptions = {
      method: "GET",
    };

    fetch(`${config.BACKEND_URL}/api/user`, requestOptions)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json();
      })
      .then((result) => {
        setData(result.data?.data);
      })
      .catch((error) => {
        setData(error);
      });
  }, []);

  useEffect(() => {
    fetchData();
  }, []);


  console.log(newScopes, " newScopesnewScopesnewScopesnewScopesnewScopesnewScopes")

  function deleteUser(id){
    
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h1 className="text-base font-semibold leading-6 text-gray-900">Roles</h1>
            </div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              <button
                type="button"
                className="block rounded-md bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                onClick={() => {setAddNewUser(true); setOpen(true); setEdit(false);}}
              >
                Add user
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                          S. NO
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Name
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          UserName
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Role
                        </th>
                        {newScopes && newScopes?.user?.length > 0 && newScopes?.user?.includes("write") || newScopes?.user?.includes("delete") ?
                          <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                            Action
                          </th>
                        : ""
                        }
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {data != null &&
                        <>
                            {data?.map((person, index) => (
                              <tr key={person.email}>
                                <td className="whitespace-nowrap py-3.5 pl-4 pr-3 text-sm text-[#222222]">{index}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{person.name}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{person.username}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-[#222222]">{person.username}</td>
                                {newScopes && newScopes?.user?.length > 0 && 
                                  <td className="relative text-left whitespace-nowrap py-4 pl-3 pr-4 text-sm font-medium sm:pr-6">
                                    {newScopes?.user?.includes("write") &&
                                      <button
                                        type="button"
                                        className="rounded-full bg-indigo-600 p-2 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                        onClick={() => {setOpen(!open); setEdit(true)}}
                                      >
                                        <PencilIcon className="h-5 w-5" aria-hidden="true" />
                                      </button>
                                    }
                                    {newScopes?.user?.includes("delete") && 
                                    <button
                                      type="button"
                                      className="rounded-full bg-indigo-600 p-2 ml-4 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                      onClick={() => deleteUser(person?.id)}
                                    >
                                      <TrashIcon className="h-5 w-5" aria-hidden="true" />
                                    </button>
                                    }
                                  </td>
                                }
                              </tr>
                            ))}
                        </>
                      }
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <UsersModal open={open} setOpen={setOpen} updateUser={edit} addNewUser={addNewUser} />
        </Sidebar>
     </QueryClientProvider>
  )
}

// export const getServerSideProps: GetServerSideProps = async (context) => {
//   const cookies = new Cookies(context.req, context.res);
//   const token = cookies.get('token');

//   // Redirect to login if no token is found
//   if (!token) {
//     return {
//       redirect: {
//         destination: '/login',
//         permanent: false,
//       },
//     };
//   }

//   return {
//     props: {}, // Return props for authenticated users
//   };
// };
