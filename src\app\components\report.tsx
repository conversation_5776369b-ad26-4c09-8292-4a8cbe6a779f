import { ArrowDownIcon, ArrowUpIcon } from "@heroicons/react/20/solid";
import { useEffect, useState } from "react";
import config from "../../../config.json";

const stats = [
  {
    name: "Total Orders",
    stat: "126",
    previousStat: "100",
    change: "26%",
    changeType: "increase",
  },
  {
    name: "Total Distributors",
    stat: "84",
    previousStat: "58",
    change: "44.82%",
    changeType: "increase",
  },
  {
    name: "Total Country Managers",
    stat: "11",
    previousStat: "12",
    change: "0.83%",
    changeType: "decrease",
  },
];

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export default function Report({type, title}) {

  const [apiData, setapiData] = useState<any>([]);
  
  useEffect(() => {

      const requestOptions = {
        method: "GET",
      };
    
      fetch(`${config.BACKEND_URL}/${type}`, requestOptions)
        .then(response => {
          if (!response.ok) {
            throw new Error("Network response was not ok");
          }
          return response.json();
        })
        .then(result => {
          setapiData( result );
        })
        .catch(error => {
          setapiData(null);  
        });
  }, []);


  return (
    <div>
      <h3 className="text-base font-semibold leading-6 text-gray-900">
        {title}
      </h3>
      {apiData?.length > 0 && 
        <dl className={`mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-white shadow md:grid-cols-${apiData.length} md:divide-x md:divide-y-0`}>
          {apiData?.map((item) => (
            <div key={item.status} className="px-4 py-5 sm:p-6">
              <dt className="text-base font-normal text-gray-900">{item.status.toUpperCase()}</dt>
              <dd className="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div className="flex items-baseline text-2xl font-semibold text-indigo-600">
                  {item.currentCount}
                  <span className="ml-2 text-sm font-medium text-[#222222]">
                    from {item.previousCount}
                  </span>
                </div>

                <div
                  className={classNames(
                    item.changeType === "increased"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800",
                    "inline-flex items-baseline rounded-full px-2.5 py-0.5 text-sm font-medium md:mt-2 lg:mt-0"
                  )}
                >
                  {item.changeType === "increased" ? (
                    <ArrowUpIcon
                      className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-green-500"
                      aria-hidden="true"
                    />
                  ) : (
                    <ArrowDownIcon
                      className="-ml-1 mr-0.5 h-5 w-5 flex-shrink-0 self-center text-red-500"
                      aria-hidden="true"
                    />
                  )}

                  <span className="sr-only">
                    {" "}
                    {item.changeType === "increase"
                      ? "Increased"
                      : "Decreased"}{" "}
                    by{" "}
                  </span>
                  {item.percentageChange.toFixed(2)} %
                </div>
              </dd>
            </div>
          ))}
        </dl>
      }
    </div>
  );
}
