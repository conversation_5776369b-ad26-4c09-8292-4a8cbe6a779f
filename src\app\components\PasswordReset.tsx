"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import config from "../../../config.json";

export default function PasswordReset() {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handlePasswordReset = async (
    event: React.FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault();
    setError("");
    setSuccess("");

    // Email validation
    if (!email.trim()) {
      setError("Email is required");
      return;
    }

    if (!validateEmail(email)) {
      setError("Please enter a valid email address");
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch(`${config.BACKEND_URL}/user/forgot-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();
      
      if (data.responseCode === 0 && data.status === "success") {
        setSuccess("Password reset link has been sent to your email");
        setTimeout(() => {
          router.push("/login");
        }, 3000);
      } else {
        // Handle error based on the new format
        const errorMessage = data.errors?.[0]?.message || "Failed to reset password";
        setError(errorMessage);
      }
    } catch (err: any) {
      setError("Something went wrong. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handlePasswordReset}>
      <label
        htmlFor="email"
        className="block text-sm font-medium leading-6 text-gray-900"
      >
        Email Address <span className="text-red-500">*</span>
      </label>
      <input
        id="email"
        value={email}
        onChange={(e) => {
          setEmail(e.target.value);
          setError(""); // Clear error when user starts typing
        }}
        className={`block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ${
          error ? "ring-red-500" : "ring-gray-300"
        } placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6`}
        disabled={isSubmitting}
      />

      {error && (
        <div className="pt-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {success && (
        <div className="pt-4">
          <p className="text-green-600">{success}</p>
        </div>
      )}

      <div className="text-center">
        <button
          type="submit"
          className="mt-8 w-full rounded-md bg-[#222222] px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Processing..." : "Reset Password"}
        </button>
      </div>
    </form>
  );
}
