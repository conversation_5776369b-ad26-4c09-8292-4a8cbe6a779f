import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export function middleware(req: NextRequest) {
  const token = req.cookies.get("token")?.value;

  // Redirect to login if no token is found, except on the login page and reset-password page
  if (!token && 
      !req.nextUrl.pathname.startsWith("/login") && 
      !req.nextUrl.pathname.startsWith("/reset-password")) {
    return NextResponse.redirect(new URL("/login", req.url));
  }

  return NextResponse.next();
}

// Apply middleware only to protected routes
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)']
  // matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
