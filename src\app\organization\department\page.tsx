"use client";
import React, { useEffect, useState, Fragment, useRef } from "react";
import Sidebar from "../../components/sidebar";
import { QueryClientProvider, QueryClient } from "@tanstack/react-query";
import { Dialog, Transition } from "@headlessui/react";
import axios from "axios";
import config from "../../../../config.json";
import { TrashIcon, XMarkIcon } from "@heroicons/react/20/solid";
import { DeleteConfirmationModal } from "@/app/components/deleteConfirmationModal";
import Cookies from "js-cookie";

const queryClient = new QueryClient();
export default function Department() {
  const [DepartmentTable, setDepartmentsTable] = useState([]);
  const [open, setOpen] = useState(false);
  const [error, setError] = useState("");
  const cancelButtonRef = useRef(null);

  const [currentId, setCurrentId] = useState(null);
  const [currentType, setCurrentType] = useState("");
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  async function deleteDepartment(id: any) {
    setCurrentId(id);
    setCurrentType("department_type");
    setConfirmationOpen(true);
  }

  async function fetchDepartmentTable() {
    let response = await axios.request({
      method: "get",
      url: `${config.BACKEND_URL}/department_type`,
      headers: {
        "Content-Type": "application/json",
      },
    });
    let data = response.data.data.data.map((x: any) => {
      return {
        id: x._id,
        department: x.department,
      };
    });
    return data;
  }
  
  async function sendDepartmentCreationData() {
    const department = (document.getElementById("name") as HTMLFormElement)
      .value;

    if (!department.trim()) {
      setError("Please fill in all the fields.");
      return;
    }

    await axios.request({
      method: "post",
      url: `${config.BACKEND_URL}/department_type`,
      headers: {
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        department: department,
      }),
    });
    setError("");
    setOpen(false);
    const data = await fetchDepartmentTable();
    setDepartmentsTable(data);
  }
  useEffect(() => {
    const fetchData = async () => {
      const data = await fetchDepartmentTable();
      setDepartmentsTable(data);
    };

    fetchData();
  }, [open]);

  const scopes = Cookies.get("type");
  const newScopes = scopes && JSON.parse(scopes);

  return (
    <QueryClientProvider client={queryClient}>
      <Sidebar>
        <Transition.Root show={open} as={Fragment}>
          <Dialog
            className="relative z-10"
            initialFocus={cancelButtonRef}
            onClose={setOpen}
          >
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
            </Transition.Child>
            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
              <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                  enterTo="opacity-100 translate-y-0 sm:scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                  leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                >
                  <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                    <div className="flex justify-end">
                      <XMarkIcon
                        onClick={() => setOpen(false)}
                        className="cursor-pointer h-6 w-6 text-black"
                      />
                    </div>
                    <div>
                      <div className="mt-3 text-center sm:mt-5">
                        <div className="isolate -space-y-px rounded-md shadow-sm">
                          <div className="relative">
                            <label
                              htmlFor="name"
                              className="absolute -top-2 left-2 inline-block bg-white px-1 text-xs font-medium text-gray-900"
                            >
                              Department
                            </label>
                            <input
                              type="text"
                              name="name"
                              id="name"
                              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                          </div>
                          <br></br>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 flex justify-center sm:mt-6 sm:gap-3">
                      <button
                        type="button"
                        className="inline-flex w-[30%] justify-center rounded-md text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 text-sm font-semibold shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2  sm:col-start-2"
                        onClick={() => {
                          setOpen(false);
                        }}
                      >
                        Cancel
                      </button>
                      <button
                        type="button"
                        className="inline-flex w-[30%] justify-center rounded-md bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm  focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2  sm:col-start-2"
                        onClick={() => {
                          sendDepartmentCreationData();
                          setOpen(false);
                        }}
                      >
                        Create
                      </button>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition.Root>

        <div className="px-4 pt-4 sm:px-6 lg:px-8">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto"></div>
            <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
              {error && <p className="text-red-600 font-bold">{error}</p>}
              <button
                onClick={() => setOpen(true)}
                type="button"
                className="block rounded-md bg-[#222222] px-3 py-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
              >
                Add Department
              </button>
            </div>
          </div>
          <div className="mt-8 flow-root">
            <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
              <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                <table className="rounded-lg shadow-sm min-w-full divide-y divide-gray-300">
                  <thead className="bg-[#E3D7D1] rounded-tl-lg rounded-tr-lg">
                    <tr>
                      <th
                        scope="col"
                        className="rounded-tl-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                      >
                        Department
                      </th>
                      {newScopes &&
                      newScopes.department.length > 0 &&
                      newScopes.department.includes("delete") ? (
                        <th
                          scope="col"
                          className="rounded-tr-lg px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Action
                        </th>
                      ) : (
                        ""
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {DepartmentTable.map((x: any) => (
                      <tr key={x.id} className="even:bg-gray-50">
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                          {x.department}
                        </td>
                        {newScopes &&
                          newScopes.department.length > 0 &&
                          newScopes.department.includes("delete") && (
                            <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                              <button
                                onClick={() => {
                                  deleteDepartment(x.id);
                                }}
                                type="button"
                                className="block rounded-full bg-[#222222] p-2 text-center text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                              >
                                <TrashIcon
                                  className="h-5 w-5"
                                  aria-hidden="true"
                                />
                              </button>
                            </td>
                          )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        {confirmationOpen && (
          <DeleteConfirmationModal
            id={currentId}
            type={currentType}
            open={confirmationOpen}
            setOpen={setConfirmationOpen}
            fetchDistributorData={fetchDepartmentTable}
            setTableData={setDepartmentsTable}
          />
        )}
      </Sidebar>
    </QueryClientProvider>
  );
}
