import { LoginData, LoginResponse } from "../../interfaces";
import config from "../../../../config.json";

export async function loginUser(loginData: LoginData): Promise<LoginResponse> {
  const response = await fetch(`${config.BACKEND_URL}/user/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(loginData),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.error || "Login failed");
  }

  return data;
}

export async function loginUserDepartment(
  loginData: LoginData
): Promise<LoginResponse> {
  console.log(loginData, " loginDataaaa");
  const newData = {
    email: loginData.username,
    password: loginData.password,
  };
  const response = await fetch(
    `${config.BACKEND_URL}/department_people/login`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(newData),
    }
  );
  const data = await response.json();

  if (!response.ok) {
    throw new Error(data.error || "Login failed");
  }

  return data;
}

export function formatToAMPM(dateString) {
  const date = new Date(dateString);

  let hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12;
  hours = hours ? hours : 12;

  const minutesStr = minutes < 10 ? "0" + minutes : minutes;
  const secondsStr = seconds < 10 ? "0" + seconds : seconds;

  const formattedTime = `${hours}:${minutesStr}:${secondsStr} ${ampm}`;

  return formattedTime;
}
