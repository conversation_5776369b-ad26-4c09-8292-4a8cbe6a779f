import { Fragment, useState } from "react";
import { Dialog, Transition } from "@headlessui/react";
import { CheckIcon } from "@heroicons/react/24/outline";
import axios from "axios";
import config from "../../../config.json";
import Cookies from "js-cookie";

export default function ConfirmationModal({
  setBulkProcessLoading,
  setBulkProcessError,
  fetchData,
  open,
  setOpen,
  selected,
  setSelected,
  passedItems,
  itemType = "Order",
}) {
  const [loader, setLoader] = useState(false);

  async function bulkProcess() {
    try {
      setLoader(true);
      if (itemType === "Shipment") {
        let allShipments = selected && selected.length > 0 ? selected : [];

        const shipmentIds = { shipmentIds: allShipments };

        let response = await axios.request({
          method: "post",
          url: `${config.BACKEND_URL}/shipment/bulkProcess`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          data: shipmentIds,
        });

        if (response.status == 200) {
          setLoader(false);
          setTimeout(() => {
            setBulkProcessLoading(false);
            fetchData();
            setOpen(false);
            if (response.data.data?.failedShipments?.length > 0) {
              const errorMessages = response.data.data.failedShipments
                .map(
                  (shipment) => `${shipment.shipmentName}: ${shipment.error}`
                )
                .join("@@");

              setBulkProcessError(errorMessages);
            } else {
              setBulkProcessError(response.data.data.message);
            }
          }, 1000);
        }
      } else {
        let allOrders = selected && selected.length > 0 ? selected : [];
        let response = await axios.request({
          method: "post",
          url: `${config.BACKEND_URL}/action/autoAllocate`,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${Cookies.get("token")}`,
          },
          data: allOrders,
        });
        if (response.status == 200) {
          setLoader(false);
          setTimeout(() => {
            setBulkProcessLoading(false);
            fetchData();
            setOpen(false);
            if (response.data?.data?.failedOrders?.length > 0) {
              const errorMessages = response.data.data.failedOrders
                .map((order) => `${order.orderName}: ${order.error}`)
                .join("@@");
              setBulkProcessError(errorMessages);
            } else {
              setBulkProcessError(response.data.data.message);
            }
          }, 1000);
        }
      }
    } catch (error: any) {
      setBulkProcessLoading(false);
      setOpen(false);
      setLoader(false);
      if (error.response?.data?.errors?.length > 0) {
        setBulkProcessError(error.response.data.errors[0].message);
      } else {
        setBulkProcessError("Something went wrong while bulk processing.");
      }
    }
  }

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog className="relative z-10" onClose={setOpen}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-sm sm:p-6">
                <div>
                  {/* <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                    <CheckIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                  </div> */}
                  <div className="mt-3 text-center sm:mt-5">
                    {
                      <Dialog.Title
                        as="h3"
                        className="text-base font-semibold leading-6 text-gray-900"
                      >
                        {itemType === "Shipment"
                          ? "Are You Sure? This action will process selected pending shipments."
                          : "Are You Sure? This action will process selected orders."}
                      </Dialog.Title>
                    }
                    <div className="mt-2">
                      {/* <p className="text-sm text-[#222222]">
                        Lorem ipsum dolor sit amet consectetur adipisicing elit. Consequatur amet labore.
                      </p> */}
                    </div>
                  </div>
                </div>
                <div className="mt-5 flex sm:mt-6">
                  {loader ? (
                    <button
                      disabled
                      type="button"
                      className={`mr-2 inline-flex w-2/4 justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600`}
                    >
                      <svg
                        aria-hidden="true"
                        role="status"
                        className="inline mr-3 w-4 h-4 text-white animate-spin"
                        viewBox="0 0 100 101"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                          fill="#E5E7EB"
                        ></path>
                        <path
                          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                          fill="currentColor"
                        ></path>
                      </svg>
                      Loading...
                    </button>
                  ) : (
                    <button
                      type="button"
                      // disabled={selected.length === 0}
                      className={`mr-2 inline-flex w-2/4 justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 ${"cursor-pointer"}`}
                      onClick={() => bulkProcess()}
                    >
                      Confirm
                    </button>
                  )}

                  <button
                    type="button"
                    className="inline-flex w-2/4 justify-center rounded-md bg-red-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-300 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-300"
                    onClick={() => setOpen(false)}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
