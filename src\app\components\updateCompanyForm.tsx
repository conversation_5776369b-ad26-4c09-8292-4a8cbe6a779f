import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useCallback, useState } from "react";
import config from "../../../config.json";

type FormData = {
  name: string;
  note: string;
};

export default function UpdateCompanyForm({
  editId,
  fetchCompanyData,
  setOpen,
}: {
  editId: string;
  fetchCompanyData: any;
  setOpen: any;
}) {
  const updateCompany = useMutation({
    mutationFn: async (updatedData: FormData) => {
      const requestOptions: RequestInit = {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedData),
      };

      const response = await fetch(
        `${config.BACKEND_URL}/company/${editId}`,
        requestOptions
      );
      fetchCompanyData();
      setOpen(false);
      return await response.json();
    },
  });
  const [formData, setFormData] = useState<any>({});

  const fetchData = useCallback(async (id: string) => {
    const response = await fetch(`${config.BACKEND_URL}/company/${id}`);
    if (!response.ok) throw new Error("Network response was not ok");
    const res = await response.json();
    setFormData(res?.data?.data);
    return res;
  }, []);

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ["users", editId],
    queryFn: () => fetchData(editId),
  });

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching data.</div>;

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [event.target.name]: event.target.value,
    }));
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (!formData.name.trim()) {
      return;
    }
    updateCompany.mutate(formData);
  };


  return (
    <div>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <h2 className="text-center font-semibold leading-7 text-gray-900">
              Update Company
            </h2>
          </div>

          <input
                    type="hidden"
                    name="shopifyId"
                    id="noteName"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    value={formData.shopifyCompanyId}
                  />

          <div className="pb-6">
            <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
              <div className="sm:col-span-3">
                <label
                  htmlFor="noteName"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Name
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="name"
                    id="noteName"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="sm:col-span-3">
                <label
                  htmlFor="note"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Note
                </label>
                <div className="mt-2">
                  <input
                    type="text"
                    name="note"
                    id="note"
                    autoComplete="off"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                    value={formData.note}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex items-center justify-center gap-x-6">
          <button
            type="button"
            className="text-sm font-semibold leading-6 text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md w-[30%]"
            onClick={() => setOpen(false)}
          >
            {" "}
            Cancel{" "}
          </button>
          <button
            type="submit"
            className="rounded-md  w-[30%] bg-[#222222]px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
          >
            Save
          </button>
        </div>
      </form>
    </div>
  );
}
