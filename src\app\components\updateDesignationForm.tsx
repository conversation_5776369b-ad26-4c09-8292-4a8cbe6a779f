import React, { useState, useEffect, useCallback } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import config from "../../../config.json";

type UserGroup = {
  _id: string;
  name: string;
};

type FormData = {
  [key: string]: any;
};

export default function UpdateDesignationForm({
  editId,
  fetchDesignationData,
  setOpen,
  setDesignationsTable
}: {
  editId: string;
  fetchDesignationData: any;
  setOpen: any;
  setDesignationsTable: any
}) {
  const [formData, setFormData] = useState<FormData>({});
  

  const fetchData = useCallback(async (id: string) => {
    const response = await fetch(
      `${config.BACKEND_URL}/designation/${id}`
    );
    if (!response.ok) throw new Error("Network response was not ok");
    const res = await response.json();
    setFormData(res.data.data);
    return res;
  }, []);

  const { data, isLoading, error, isError, refetch } = useQuery({
    queryKey: ["users", editId],
    queryFn: () => fetchData(editId),
  });

  // Mutation for updating user details
  const updateUser = useMutation({
    mutationFn: async (data: FormData) => {
      const response = await fetch(`${config.BACKEND_URL}/designation/${editId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      })
      
        setOpen(false);
        const fetchResult = await fetchDesignationData();
        setDesignationsTable(fetchResult);  
    }
  });

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    updateUser.mutate(formData);
   
  };

  console.log(formData, " form data")

  return (
    <form onSubmit={handleSubmit}>
      <div className="pb-6">
        <div className="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-1">
         
          <div className="sm:col-span-3">
            <label className="block text-sm font-medium leading-6 text-gray-900">
              Name
            </label>
            <input
              className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
              type="text"
              name="name"
              value={formData.name || ""}
              onChange={handleInputChange}
            />
          </div>
          <div className="sm:col-span-3">
          <p className="mt-1 text-sm leading-6 text-gray-600">Is it country manager??</p>
          <div className="mt-6 space-y-6 sm:flex sm:items-center sm:space-x-10 sm:space-y-0">
  <div className="flex items-center">
    <input
      checked={formData.isCountryManager === true}
      id="countryManagerYes"
      name="countryManager"
      type="radio"
      value="true"
      className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
      onChange={(e) => setFormData({...formData, isCountryManager: true})}
    />
    <label htmlFor="countryManagerYes" className="ml-3 block text-sm font-medium leading-6 text-gray-900">
      Yes
    </label>
  </div>
  <div className="flex items-center">
    <input
      checked={formData.isCountryManager === false}
      id="countryManagerNo"
      name="countryManager"
      type="radio"
      value="false"
      className="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"
      onChange={(e) => setFormData({...formData, isCountryManager: false})}
    />
    <label htmlFor="countryManagerNo" className="ml-3 block text-sm font-medium leading-6 text-gray-900">
      No
    </label>
  </div>
</div>

          </div>

          <div className="mt-6 flex justify-center items-center border-t-2 gap-x-6">
            <button
              type="button"
              className="text-sm w-[30%] font-semibold leading-6 mt-4 text-[#1D1C21] border-[#1D1C21] border-2 px-2 py-1 rounded-md"
              onClick={() => setOpen(false)}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="rounded-md w-[30%] mt-4 bg-[#222222] px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
            >
              Update User
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
