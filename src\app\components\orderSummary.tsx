import React from 'react';
import { useQuery } from '@tanstack/react-query';


interface OrderSummaryProps {
  setapiDataOrderSummary: any;
  additionalClass: string;
  orderTotalKey: string;
  orderCreationDate: string;
  orderTotalItems: string;
  orderCountry: string;
  orderHeading: string;
  orderTotal: string;
  creationDate: string;
  totalItems: string;
  country: string;
  page: string,
  id: any
}




const OrderSummary: React.FC<OrderSummaryProps> = ({
  additionalClass,
  orderCountry,
  orderTotalItems,
  orderCreationDate,
  orderTotalKey,
  orderHeading,
  orderTotal,
  creationDate,
  totalItems,
  country,
  page,
  id,
  setapiDataOrderSummary
}) => {

  async function fetchData(endpoint: String){
    const response = await fetch(`https://ujhf7m05d0.execute-api.ap-south-1.amazonaws.com/api/${endpoint}/${id}`);
    if (!response.ok) {
      throw new Error('Network response was not ok');
    }
      return response.json();
  }
  
  const { data, isLoading, error, isError, refetch } = useQuery({
      queryKey: ['users'],
      queryFn: () => fetchData(page),
  });

  console.log(data, " data order summary")

  setapiDataOrderSummary(data)


  return (
    <div className={`p-4 border border-gray-300 ${additionalClass}`}>
      <h2 className="text-lg mb-2">{orderHeading}</h2>
      <p className="font-normal">{orderTotalKey} {orderTotal}</p>
      <p className="font-normal">{orderCreationDate} {creationDate}</p>
      <p className="font-normal">{orderTotalItems} {totalItems}</p>
      <p className="font-normal">{orderCountry} {country}</p>
    </div>
  );
};

export default OrderSummary;
