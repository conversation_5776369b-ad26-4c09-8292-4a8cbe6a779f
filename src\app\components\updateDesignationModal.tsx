import { Fragment, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import UpdateRolesForm from "./updateRolesForm";
import NewRolesForm from "./newRolesForm";
import { XMarkIcon } from "@heroicons/react/20/solid";
import UpdateDesignationForm from "./updateDesignationForm";

export default function UpdateDesignationModal({
  editId, open, fetchDesignationTable, setOpen, setDesignationsTable
}) {
  const cancelButtonRef = useRef(null);

  return (
    <Transition.Root show={open} as={Fragment}>
      <Dialog
        className="relative z-10"
        initialFocus={cancelButtonRef}
        onClose={setOpen}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-[#222222] bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:mb-8 sm:w-full sm:max-w-lg sm:p-6 mt-24">
                <div className="flex justify-end">
                  <XMarkIcon
                    onClick={() => setOpen(false)}
                    className="cursor-pointer h-6 w-6 text-black"
                  />
                </div>
                <UpdateDesignationForm
                  setOpen={setOpen}
                  editId={editId}
                  fetchDesignationData={fetchDesignationTable}
                  setDesignationsTable={setDesignationsTable}
                />
              
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
